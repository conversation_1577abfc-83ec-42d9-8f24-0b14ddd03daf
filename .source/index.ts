// @ts-nocheck -- skip type checking
import * as docs_25 from "../content/docs/api/examples/index.mdx?collection=docs&hash=1754431018670"
import * as docs_24 from "../content/docs/sources/workable.mdx?collection=docs&hash=1754431018670"
import * as docs_23 from "../content/docs/sources/weworkremotely-rss.mdx?collection=docs&hash=1754431018670"
import * as docs_22 from "../content/docs/sources/jobdata-api.mdx?collection=docs&hash=1754431018670"
import * as docs_21 from "../content/docs/sources/index.mdx?collection=docs&hash=1754431018670"
import * as docs_20 from "../content/docs/technical/zustand-reference.mdx?collection=docs&hash=1754431018670"
import * as docs_19 from "../content/docs/technical/zustand-performance.mdx?collection=docs&hash=1754431018670"
import * as docs_18 from "../content/docs/technical/roadmap.mdx?collection=docs&hash=1754431018670"
import * as docs_17 from "../content/docs/technical/mock-server.mdx?collection=docs&hash=1754431018670"
import * as docs_16 from "../content/docs/technical/index.mdx?collection=docs&hash=1754431018670"
import * as docs_15 from "../content/docs/reference/index.mdx?collection=docs&hash=1754431018670"
import * as docs_14 from "../content/docs/reference/changelog.mdx?collection=docs&hash=1754431018670"
import * as docs_13 from "../content/docs/guides/index.mdx?collection=docs&hash=1754431018670"
import * as docs_12 from "../content/docs/development/webhook-debugging.mdx?collection=docs&hash=1754431018670"
import * as docs_11 from "../content/docs/development/testing.mdx?collection=docs&hash=1754431018670"
import * as docs_10 from "../content/docs/development/index.mdx?collection=docs&hash=1754431018670"
import * as docs_9 from "../content/docs/development/environment-setup.mdx?collection=docs&hash=1754431018670"
import * as docs_8 from "../content/docs/api/versioning.mdx?collection=docs&hash=1754431018670"
import * as docs_7 from "../content/docs/api/rate-limiting.mdx?collection=docs&hash=1754431018670"
import * as docs_6 from "../content/docs/api/migrations.mdx?collection=docs&hash=1754431018670"
import * as docs_5 from "../content/docs/api/interactive.mdx?collection=docs&hash=1754431018670"
import * as docs_4 from "../content/docs/api/index.mdx?collection=docs&hash=1754431018670"
import * as docs_3 from "../content/docs/api/examples.mdx?collection=docs&hash=1754431018670"
import * as docs_2 from "../content/docs/api/authentication.mdx?collection=docs&hash=1754431018670"
import * as docs_1 from "../content/docs/index.mdx?collection=docs&hash=1754431018670"
import * as docs_0 from "../content/docs/getting-started.mdx?collection=docs&hash=1754431018670"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const docs = _runtime.docs<typeof _source.docs>([{ info: {"path":"getting-started.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/getting-started.mdx"}, data: docs_0 }, { info: {"path":"index.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/index.mdx"}, data: docs_1 }, { info: {"path":"api/authentication.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/api/authentication.mdx"}, data: docs_2 }, { info: {"path":"api/examples.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/api/examples.mdx"}, data: docs_3 }, { info: {"path":"api/index.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/api/index.mdx"}, data: docs_4 }, { info: {"path":"api/interactive.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/api/interactive.mdx"}, data: docs_5 }, { info: {"path":"api/migrations.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/api/migrations.mdx"}, data: docs_6 }, { info: {"path":"api/rate-limiting.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/api/rate-limiting.mdx"}, data: docs_7 }, { info: {"path":"api/versioning.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/api/versioning.mdx"}, data: docs_8 }, { info: {"path":"development/environment-setup.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/development/environment-setup.mdx"}, data: docs_9 }, { info: {"path":"development/index.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/development/index.mdx"}, data: docs_10 }, { info: {"path":"development/testing.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/development/testing.mdx"}, data: docs_11 }, { info: {"path":"development/webhook-debugging.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/development/webhook-debugging.mdx"}, data: docs_12 }, { info: {"path":"guides/index.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/guides/index.mdx"}, data: docs_13 }, { info: {"path":"reference/changelog.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/reference/changelog.mdx"}, data: docs_14 }, { info: {"path":"reference/index.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/reference/index.mdx"}, data: docs_15 }, { info: {"path":"technical/index.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/technical/index.mdx"}, data: docs_16 }, { info: {"path":"technical/mock-server.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/technical/mock-server.mdx"}, data: docs_17 }, { info: {"path":"technical/roadmap.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/technical/roadmap.mdx"}, data: docs_18 }, { info: {"path":"technical/zustand-performance.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/technical/zustand-performance.mdx"}, data: docs_19 }, { info: {"path":"technical/zustand-reference.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/technical/zustand-reference.mdx"}, data: docs_20 }, { info: {"path":"sources/index.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/sources/index.mdx"}, data: docs_21 }, { info: {"path":"sources/jobdata-api.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/sources/jobdata-api.mdx"}, data: docs_22 }, { info: {"path":"sources/weworkremotely-rss.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/sources/weworkremotely-rss.mdx"}, data: docs_23 }, { info: {"path":"sources/workable.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/sources/workable.mdx"}, data: docs_24 }, { info: {"path":"api/examples/index.mdx","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/api/examples/index.mdx"}, data: docs_25 }], [{"info":{"path":"meta.json","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/meta.json"},"data":{"title":"Documentation","pages":["index","getting-started","---","guides","sources","api","---","development","technical","reference"]}}, {"info":{"path":"api/meta.json","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/api/meta.json"},"data":{"title":"API Reference","pages":["index","interactive","authentication","rate-limiting","versioning","examples","migrations"]}}, {"info":{"path":"api/openapi.json","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/api/openapi.json"},"data":{}}, {"info":{"path":"guides/meta.json","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/guides/meta.json"},"data":{"title":"Guides","pages":["index"]}}, {"info":{"path":"reference/meta.json","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/reference/meta.json"},"data":{"title":"Reference","pages":["index","changelog"]}}, {"info":{"path":"development/meta.json","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/development/meta.json"},"data":{"title":"Development","pages":["index","environment-setup","testing","webhook-debugging"]}}, {"info":{"path":"technical/meta.json","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/technical/meta.json"},"data":{"title":"Technical","pages":["index","roadmap","mock-server","zustand-performance","zustand-reference"]}}, {"info":{"path":"sources/meta.json","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/sources/meta.json"},"data":{"title":"Data Sources","pages":["index","jobdata-api","workable","weworkremotely-rss"]}}, {"info":{"path":"api/examples/meta.json","absolutePath":"/Users/<USER>/Sites/bordfeed/content/docs/api/examples/meta.json"},"data":{"title":"Examples","pages":["index"]}}])