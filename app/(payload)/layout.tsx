import type { <PERSON>ada<PERSON> } from "next";
import React from "react";
import { PayloadProviders } from "./providers";
import config from "@payload-config";
import "./custom.scss";

type Args = {
  children: React.ReactNode;
};

const Layout = ({ children }: Args) => {
  return (
    <html lang="en">
      <body>
        <div id="payload-root">
          <PayloadProviders config={config}>{children}</PayloadProviders>
        </div>
      </body>
    </html>
  );
};

export default Layout;

export const metadata: Metadata = {
  title: "Bordfeed Admin",
  description: "PayloadCMS Admin Panel for Bordfeed",
};
