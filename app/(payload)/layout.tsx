/* THIS FILE WAS GENERATED AUTOMATICALLY BY PAYLOAD. */
/* DO NOT MODIFY IT BECAUSE IT COULD BE REWRITTEN AT ANY TIME. */
import type { Metadata } from "next";

import config from "@payload-config";
import { RootLayout } from "@payloadcms/next/layouts";
import { importMap } from "@payloadcms/next/utilities";

import "./custom.scss";

type Args = {
  children: React.ReactNode;
};

const Layout = ({ children }: Args) =>
  RootLayout({ children, config, importMap });

export default Layout;

export const metadata: Metadata = {
  title: "Bordfeed Admin",
  description: "PayloadCMS Admin Panel for Bordfeed",
};
