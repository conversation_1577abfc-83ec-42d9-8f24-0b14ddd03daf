import { <PERSON>ada<PERSON> } from "next";
import { createServerClient } from "@/lib/supabase";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Refresh<PERSON>w, TrendingUp, TrendingDown, Minus } from "lucide-react";
import { triggerDataQualityMonitoring } from "@/lib/workflow-client";
import { redirect } from "next/navigation";

export const metadata: Metadata = {
  title: "Data Quality | Bordfeed Dashboard",
  description: "Monitor data quality metrics and AI analysis results",
};

interface DataQualityLog {
  id: string;
  timestamp: string;
  dataset_type: string;
  total_items: number;
  overall_score: number;
  overall_quality: string;
  confidence: number;
  data_completeness: number;
  schema_compliance: number;
  data_consistency: number;
  ai_extraction_success: number;
  issues_count: number;
  critical_issues_count: number;
  actions_taken: string[];
  remediation_triggered: boolean;
}

interface QualitySummary {
  hour: string;
  dataset_type: string;
  check_count: number;
  avg_score: number;
  avg_completeness: number;
  avg_schema_compliance: number;
  avg_consistency: number;
  avg_ai_success: number;
  total_critical_issues: number;
  remediations_triggered: number;
}

async function getDataQualityLogs(): Promise<DataQualityLog[]> {
  const supabase = await createServerClient();

  const { data, error } = await supabase
    .from("data_quality_logs")
    .select("*")
    .order("timestamp", { ascending: false })
    .limit(50);

  if (error) {
    console.error("Error fetching data quality logs:", error);
    return [];
  }

  return data || [];
}

async function getQualitySummary(): Promise<QualitySummary[]> {
  const supabase = await createServerClient();

  const { data, error } = await supabase
    .from("data_quality_trends")
    .select("*")
    .order("hour", { ascending: false })
    .limit(24);

  if (error) {
    console.error("Error fetching quality summary:", error);
    return [];
  }

  return data || [];
}

function getQualityColor(quality: string): string {
  switch (quality) {
    case "EXCELLENT":
      return "bg-green-500";
    case "GOOD":
      return "bg-blue-500";
    case "FAIR":
      return "bg-yellow-500";
    case "POOR":
      return "bg-orange-500";
    case "CRITICAL":
      return "bg-red-500";
    default:
      return "bg-gray-500";
  }
}

function getQualityBadgeVariant(
  quality: string
): "default" | "destructive" | "secondary" | "outline" {
  switch (quality) {
    case "EXCELLENT":
    case "GOOD":
      return "default";
    case "FAIR":
      return "secondary";
    case "POOR":
    case "CRITICAL":
      return "destructive";
    default:
      return "outline";
  }
}

function getScoreIcon(score: number, previousScore?: number) {
  if (!previousScore)
    return <Minus className="h-4 w-4 text-muted-foreground" />;

  if (score > previousScore) {
    return <TrendingUp className="h-4 w-4 text-green-500" />;
  } else if (score < previousScore) {
    return <TrendingDown className="h-4 w-4 text-red-500" />;
  } else {
    return <Minus className="h-4 w-4 text-muted-foreground" />;
  }
}

async function triggerQualityCheck() {
  "use server";

  try {
    await triggerDataQualityMonitoring({
      datasetType: "manual_trigger",
    });
  } catch (error) {
    console.error("Failed to trigger quality check:", error);
  }

  redirect("/dashboard/data-quality");
}

export default async function DataQualityPage() {
  const [qualityLogs, qualitySummary] = await Promise.all([
    getDataQualityLogs(),
    getQualitySummary(),
  ]);

  const latestQuality = qualityLogs[0];
  const previousQuality = qualityLogs[1];

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Data Quality</h1>
          <p className="text-muted-foreground">
            AI-powered data quality monitoring and analysis
          </p>
        </div>

        <form action={triggerQualityCheck}>
          <Button type="submit" variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            Run Quality Check
          </Button>
        </form>
      </div>

      {/* Current Quality Overview */}
      {latestQuality && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Overall Quality
              </CardTitle>
              <div
                className={`w-3 h-3 rounded-full ${getQualityColor(latestQuality.overall_quality)}`}
              />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {latestQuality.overall_score}/100
              </div>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                {getScoreIcon(
                  latestQuality.overall_score,
                  previousQuality?.overall_score
                )}
                <Badge
                  variant={getQualityBadgeVariant(
                    latestQuality.overall_quality
                  )}
                >
                  {latestQuality.overall_quality}
                </Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Data Completeness
              </CardTitle>
              <div className="text-xs text-muted-foreground">
                {(latestQuality.confidence * 100).toFixed(0)}% confidence
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {(latestQuality.data_completeness * 100).toFixed(1)}%
              </div>
              <p className="text-xs text-muted-foreground">
                Schema: {(latestQuality.schema_compliance * 100).toFixed(1)}%
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                AI Success Rate
              </CardTitle>
              <div className="text-xs text-muted-foreground">
                {latestQuality.total_items} items
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {(latestQuality.ai_extraction_success * 100).toFixed(1)}%
              </div>
              <p className="text-xs text-muted-foreground">
                Consistency: {(latestQuality.data_consistency * 100).toFixed(1)}
                %
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Issues</CardTitle>
              {latestQuality.remediation_triggered && (
                <Badge variant="outline" className="text-xs">
                  Auto-fixed
                </Badge>
              )}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-500">
                {latestQuality.critical_issues_count}
              </div>
              <p className="text-xs text-muted-foreground">
                {latestQuality.issues_count} total issues
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Quality History and Trends */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Recent Quality Checks</CardTitle>
          </CardHeader>
          <CardContent>
            {qualityLogs.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No quality checks available. Trigger a quality check to get
                started.
              </div>
            ) : (
              <div className="space-y-4">
                {qualityLogs.slice(0, 8).map((log) => (
                  <div
                    key={log.id}
                    className="flex items-center justify-between p-3 rounded border"
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className={`w-3 h-3 rounded-full ${getQualityColor(log.overall_quality)}`}
                      />
                      <div>
                        <div className="text-sm font-medium">
                          {new Date(log.timestamp).toLocaleString()}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {log.total_items} items | {log.dataset_type}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 text-right">
                      <div className="text-sm font-medium">
                        {log.overall_score}/100
                      </div>
                      <Badge
                        variant={getQualityBadgeVariant(log.overall_quality)}
                      >
                        {log.overall_quality}
                      </Badge>
                      {log.critical_issues_count > 0 && (
                        <Badge variant="destructive" className="text-xs">
                          {log.critical_issues_count} critical
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quality Trends (24h)</CardTitle>
          </CardHeader>
          <CardContent>
            {qualitySummary.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No trend data available.
              </div>
            ) : (
              <div className="space-y-3">
                {qualitySummary.slice(0, 12).map((summary) => (
                  <div
                    key={`${summary.hour}-${summary.dataset_type}`}
                    className="flex items-center justify-between p-2 rounded hover:bg-muted/50"
                  >
                    <div className="flex items-center gap-3">
                      <span className="font-medium text-sm">
                        {new Date(summary.hour).toLocaleTimeString([], {
                          hour: "2-digit",
                          minute: "2-digit",
                        })}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {summary.check_count} checks
                      </span>
                    </div>

                    <div className="text-right">
                      <div className="text-sm font-medium">
                        {Math.round(summary.avg_score)}/100
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {summary.total_critical_issues > 0 &&
                          `${summary.total_critical_issues} issues`}
                        {summary.remediations_triggered > 0 &&
                          ` | ${summary.remediations_triggered} fixed`}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Metrics Breakdown */}
      {latestQuality && (
        <Card>
          <CardHeader>
            <CardTitle>Quality Metrics Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="text-center p-4 border rounded">
                <div className="text-2xl font-bold text-blue-500">
                  {(latestQuality.data_completeness * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground">
                  Data Completeness
                </div>
              </div>
              <div className="text-center p-4 border rounded">
                <div className="text-2xl font-bold text-green-500">
                  {(latestQuality.schema_compliance * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground">
                  Schema Compliance
                </div>
              </div>
              <div className="text-center p-4 border rounded">
                <div className="text-2xl font-bold text-purple-500">
                  {(latestQuality.data_consistency * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground">
                  Data Consistency
                </div>
              </div>
              <div className="text-center p-4 border rounded">
                <div className="text-2xl font-bold text-orange-500">
                  {(latestQuality.ai_extraction_success * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground">
                  AI Extraction Success
                </div>
              </div>
            </div>

            {latestQuality.actions_taken.length > 0 && (
              <div className="mt-4 p-4 bg-muted rounded">
                <h4 className="font-medium mb-2">Actions Taken:</h4>
                <div className="flex flex-wrap gap-2">
                  {latestQuality.actions_taken.map((action, index) => (
                    <Badge key={index} variant="outline">
                      {action.replace(/-/g, " ")}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {latestQuality && (
        <div className="text-muted-foreground text-xs">
          Last updated: {new Date(latestQuality.timestamp).toLocaleString()}
        </div>
      )}
    </div>
  );
}
