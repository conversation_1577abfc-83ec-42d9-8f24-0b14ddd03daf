import { <PERSON>ada<PERSON> } from "next";
import { createServerClient } from "@/lib/supabase";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { triggerSystemHealthMonitoring } from "@/lib/workflow-client";
import { redirect } from "next/navigation";

export const metadata: Metadata = {
  title: "System Health | Bordfeed Dashboard",
  description: "Monitor system health and performance metrics",
};

interface HealthLog {
  id: string;
  timestamp: string;
  overall_status: "healthy" | "degraded" | "unhealthy";
  database_status: string;
  database_latency: number;
  workflow_status: string;
  workflow_latency: number;
  apify_status: string;
  apify_latency: number;
  slack_status: string;
  slack_latency: number;
  actions: string[];
  created_at: string;
}

interface HealthSummary {
  hour: string;
  overall_status: string;
  check_count: number;
  avg_db_latency: number;
  avg_workflow_latency: number;
  avg_apify_latency: number;
  avg_slack_latency: number;
  latest_check: string;
}

async function getHealthLogs(): Promise<HealthLog[]> {
  const supabase = await createServerClient();

  const { data, error } = await supabase
    .from("system_health_logs")
    .select("*")
    .order("timestamp", { ascending: false })
    .limit(50);

  if (error) {
    console.error("Error fetching health logs:", error);
    return [];
  }

  return data || [];
}

async function getHealthSummary(): Promise<HealthSummary[]> {
  const supabase = await createServerClient();

  const { data, error } = await supabase
    .from("system_health_summary")
    .select("*")
    .order("hour", { ascending: false })
    .limit(24);

  if (error) {
    console.error("Error fetching health summary:", error);
    return [];
  }

  return data || [];
}

function getStatusColor(status: string): string {
  switch (status) {
    case "healthy":
      return "bg-green-500";
    case "degraded":
      return "bg-yellow-500";
    case "error":
    case "unhealthy":
      return "bg-red-500";
    default:
      return "bg-gray-500";
  }
}

function getStatusBadgeVariant(
  status: string
): "default" | "destructive" | "secondary" | "outline" {
  switch (status) {
    case "healthy":
      return "default";
    case "degraded":
      return "secondary";
    case "error":
    case "unhealthy":
      return "destructive";
    default:
      return "outline";
  }
}

async function triggerHealthCheck() {
  "use server";

  try {
    await triggerSystemHealthMonitoring();
  } catch (error) {
    console.error("Failed to trigger health check:", error);
  }

  redirect("/dashboard/system-health");
}

export default async function SystemHealthPage() {
  const [healthLogs, healthSummary] = await Promise.all([
    getHealthLogs(),
    getHealthSummary(),
  ]);

  const latestHealth = healthLogs[0];
  const healthyCount = healthLogs.filter(
    (log) => log.overall_status === "healthy"
  ).length;
  const uptime =
    healthLogs.length > 0 ? (healthyCount / healthLogs.length) * 100 : 0;

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">System Health</h1>
          <p className="text-muted-foreground">
            Monitor infrastructure health and performance
          </p>
        </div>

        <form action={triggerHealthCheck}>
          <Button type="submit" variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            Run Health Check
          </Button>
        </form>
      </div>

      {/* Current Status Overview */}
      {latestHealth && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Overall Status
              </CardTitle>
              <div
                className={`w-3 h-3 rounded-full ${getStatusColor(latestHealth.overall_status)}`}
              />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold capitalize">
                {latestHealth.overall_status}
              </div>
              <p className="text-xs text-muted-foreground">
                Last check:{" "}
                {new Date(latestHealth.timestamp).toLocaleTimeString()}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Uptime (50 checks)
              </CardTitle>
              <div
                className={`w-3 h-3 rounded-full ${uptime > 95 ? "bg-green-500" : uptime > 90 ? "bg-yellow-500" : "bg-red-500"}`}
              />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{uptime.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">
                {healthyCount}/{healthLogs.length} healthy
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Database</CardTitle>
              <div
                className={`w-3 h-3 rounded-full ${getStatusColor(latestHealth.database_status)}`}
              />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {latestHealth.database_latency}ms
              </div>
              <p className="text-xs text-muted-foreground capitalize">
                {latestHealth.database_status}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Services</CardTitle>
              <div className="flex gap-1">
                <div
                  className={`w-2 h-2 rounded-full ${getStatusColor(latestHealth.workflow_status)}`}
                  title="Workflow"
                />
                <div
                  className={`w-2 h-2 rounded-full ${getStatusColor(latestHealth.apify_status)}`}
                  title="Apify"
                />
                <div
                  className={`w-2 h-2 rounded-full ${getStatusColor(latestHealth.slack_status)}`}
                  title="Slack"
                />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-sm space-y-1">
                <div className="flex justify-between">
                  <span>Workflow:</span>
                  <span>{latestHealth.workflow_latency}ms</span>
                </div>
                <div className="flex justify-between">
                  <span>Apify:</span>
                  <span>{latestHealth.apify_latency}ms</span>
                </div>
                <div className="flex justify-between">
                  <span>Slack:</span>
                  <span>{latestHealth.slack_latency}ms</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Recent Health Checks */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Health Checks</CardTitle>
        </CardHeader>
        <CardContent>
          {healthLogs.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No health check data available. Trigger a health check to get
              started.
            </div>
          ) : (
            <div className="space-y-3">
              {healthLogs.slice(0, 10).map((log) => (
                <div
                  key={log.id}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={`w-3 h-3 rounded-full ${getStatusColor(log.overall_status)}`}
                    />
                    <div>
                      <div className="text-sm font-medium">
                        {new Date(log.timestamp).toLocaleString()}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        DB: {log.database_latency}ms | Workflow:{" "}
                        {log.workflow_latency}ms | Apify: {log.apify_latency}ms
                        | Slack: {log.slack_latency}ms
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Badge variant={getStatusBadgeVariant(log.overall_status)}>
                      {log.overall_status}
                    </Badge>

                    {log.actions.length > 0 && (
                      <Badge variant="outline" className="text-xs">
                        {log.actions.length} action
                        {log.actions.length !== 1 ? "s" : ""}
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 24-Hour Summary */}
      {healthSummary.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>24-Hour Health Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {healthSummary.slice(0, 12).map((summary) => (
                <div
                  key={`${summary.hour}-${summary.overall_status}`}
                  className="flex items-center justify-between p-2 text-sm"
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={`w-2 h-2 rounded-full ${getStatusColor(summary.overall_status)}`}
                    />
                    <span className="font-medium">
                      {new Date(summary.hour).toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </span>
                    <Badge
                      variant={getStatusBadgeVariant(summary.overall_status)}
                      className="text-xs"
                    >
                      {summary.overall_status}
                    </Badge>
                  </div>

                  <div className="text-xs text-muted-foreground">
                    {summary.check_count} checks | Avg DB:{" "}
                    {Math.round(summary.avg_db_latency)}ms | Avg API:{" "}
                    {Math.round(summary.avg_apify_latency)}ms
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
