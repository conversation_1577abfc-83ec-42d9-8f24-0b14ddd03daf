import { <PERSON><PERSON><PERSON> } from "next";
import { createServerClient } from "@/lib/supabase";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardH<PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  RefreshCw,
  Play,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
} from "lucide-react";
import { triggerMonitoringCoordinator } from "@/lib/workflow-client";
import { redirect } from "next/navigation";

export const metadata: Metadata = {
  title: "Monitoring Coordinator | Bordfeed Dashboard",
  description: "Master monitoring coordination and workflow orchestration",
};

interface OverallStatus {
  system_status: string;
  data_quality_status: string;
  business_health: string;
  overall_platform_status: string;
  system_check_time: string;
  quality_check_time: string;
  business_check_time: string;
  dashboard_generated_at: string;
}

async function getOverallStatus(): Promise<OverallStatus | null> {
  const supabase = await createServerClient();

  const { data, error } = await supabase
    .from("monitoring_dashboard")
    .select("*")
    .single();

  if (error) {
    console.error("Error fetching monitoring dashboard:", error);
    return null;
  }

  return data;
}

async function getRecentSystemHealth() {
  const supabase = await createServerClient();

  const { data, error } = await supabase
    .from("system_health_logs")
    .select(
      "timestamp, overall_status, database_latency, workflow_latency, apify_latency"
    )
    .order("timestamp", { ascending: false })
    .limit(10);

  if (error) return [];
  return data || [];
}

async function getRecentDataQuality() {
  const supabase = await createServerClient();

  const { data, error } = await supabase
    .from("data_quality_logs")
    .select(
      "timestamp, overall_quality, overall_score, critical_issues_count, remediation_triggered"
    )
    .order("timestamp", { ascending: false })
    .limit(10);

  if (error) return [];
  return data || [];
}

async function getRecentBusinessLogic() {
  const supabase = await createServerClient();

  const { data, error } = await supabase
    .from("business_logic_logs")
    .select(
      "timestamp, business_health, business_value_score, efficiency_score, cost_optimization_potential"
    )
    .order("timestamp", { ascending: false })
    .limit(10);

  if (error) return [];
  return data || [];
}

function getStatusIcon(status: string) {
  switch (status.toLowerCase()) {
    case "healthy":
    case "excellent":
    case "good":
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    case "degraded":
    case "fair":
    case "warning":
      return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    case "unhealthy":
    case "critical":
    case "poor":
    case "concerning":
      return <XCircle className="h-5 w-5 text-red-500" />;
    default:
      return <Clock className="h-5 w-5 text-gray-500" />;
  }
}

function getStatusColor(status: string): string {
  switch (status.toLowerCase()) {
    case "healthy":
    case "excellent":
    case "good":
      return "bg-green-500";
    case "degraded":
    case "fair":
    case "warning":
      return "bg-yellow-500";
    case "unhealthy":
    case "critical":
    case "poor":
    case "concerning":
      return "bg-red-500";
    default:
      return "bg-gray-500";
  }
}

function getStatusBadgeVariant(
  status: string
): "default" | "destructive" | "secondary" | "outline" {
  switch (status.toLowerCase()) {
    case "healthy":
    case "excellent":
    case "good":
      return "default";
    case "degraded":
    case "fair":
    case "warning":
      return "secondary";
    case "unhealthy":
    case "critical":
    case "poor":
    case "concerning":
      return "destructive";
    default:
      return "outline";
  }
}

async function triggerCoordinatorAction(schedule: string) {
  "use server";

  try {
    await triggerMonitoringCoordinator({
      schedule: schedule as "startup" | "hourly" | "daily" | "manual",
      priority: "high",
      triggerSource: "dashboard_manual",
    });
  } catch (error) {
    console.error("Failed to trigger monitoring coordinator:", error);
  }

  redirect("/dashboard/monitoring-coordinator");
}

export default async function MonitoringCoordinatorPage() {
  const [overallStatus, systemHealth, dataQuality, businessLogic] =
    await Promise.all([
      getOverallStatus(),
      getRecentSystemHealth(),
      getRecentDataQuality(),
      getRecentBusinessLogic(),
    ]);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Monitoring Coordinator</h1>
          <p className="text-muted-foreground">
            Master orchestration of all monitoring tiers
          </p>
        </div>

        <div className="flex gap-2">
          <form action={triggerCoordinatorAction.bind(null, "manual")}>
            <Button type="submit" variant="outline" size="sm">
              <Play className="mr-2 h-4 w-4" />
              Manual Run
            </Button>
          </form>
          <form action={triggerCoordinatorAction.bind(null, "comprehensive")}>
            <Button type="submit" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              Full Analysis
            </Button>
          </form>
        </div>
      </div>

      {/* Overall Platform Status */}
      {overallStatus && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getStatusIcon(overallStatus.overall_platform_status)}
              Platform Status Overview
              <Badge
                variant={getStatusBadgeVariant(
                  overallStatus.overall_platform_status
                )}
              >
                {overallStatus.overall_platform_status}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="flex items-center gap-3 p-3 border rounded">
                <div
                  className={`w-3 h-3 rounded-full ${getStatusColor(overallStatus.system_status)}`}
                />
                <div>
                  <div className="font-medium">System Health</div>
                  <div className="text-sm text-muted-foreground capitalize">
                    {overallStatus.system_status}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {new Date(overallStatus.system_check_time).toLocaleString()}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 border rounded">
                <div
                  className={`w-3 h-3 rounded-full ${getStatusColor(overallStatus.data_quality_status)}`}
                />
                <div>
                  <div className="font-medium">Data Quality</div>
                  <div className="text-sm text-muted-foreground capitalize">
                    {overallStatus.data_quality_status}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {new Date(
                      overallStatus.quality_check_time
                    ).toLocaleString()}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 border rounded">
                <div
                  className={`w-3 h-3 rounded-full ${getStatusColor(overallStatus.business_health)}`}
                />
                <div>
                  <div className="font-medium">Business Logic</div>
                  <div className="text-sm text-muted-foreground capitalize">
                    {overallStatus.business_health}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {new Date(
                      overallStatus.business_check_time
                    ).toLocaleString()}
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-4 text-xs text-muted-foreground">
              Dashboard generated:{" "}
              {new Date(overallStatus.dashboard_generated_at).toLocaleString()}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Coordinator Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Coordinator Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
            <form action={triggerCoordinatorAction.bind(null, "startup")}>
              <Button type="submit" variant="outline" className="w-full">
                <Play className="mr-2 h-4 w-4" />
                Startup Check
              </Button>
            </form>

            <form action={triggerCoordinatorAction.bind(null, "hourly")}>
              <Button type="submit" variant="outline" className="w-full">
                <Clock className="mr-2 h-4 w-4" />
                Hourly Monitor
              </Button>
            </form>

            <form action={triggerCoordinatorAction.bind(null, "daily")}>
              <Button type="submit" variant="outline" className="w-full">
                <RefreshCw className="mr-2 h-4 w-4" />
                Daily Analysis
              </Button>
            </form>

            <form action={triggerCoordinatorAction.bind(null, "manual")}>
              <Button type="submit" variant="outline" className="w-full">
                <Play className="mr-2 h-4 w-4" />
                Manual Run
              </Button>
            </form>
          </div>

          <div className="mt-4 p-3 bg-muted rounded text-sm">
            <div className="font-medium mb-2">Execution Modes:</div>
            <ul className="space-y-1 text-xs text-muted-foreground">
              <li>
                <strong>Startup:</strong> Sequential execution for comprehensive
                initialization
              </li>
              <li>
                <strong>Hourly:</strong> Parallel execution for system health +
                data quality
              </li>
              <li>
                <strong>Daily:</strong> Sequential execution for full business
                intelligence
              </li>
              <li>
                <strong>Manual:</strong> Parallel execution for immediate
                insights
              </li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Monitoring Tier Status */}
      <div className="grid gap-6 md:grid-cols-3">
        {/* System Health */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">System Health Tier</CardTitle>
          </CardHeader>
          <CardContent>
            {systemHealth.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground text-sm">
                No recent health checks
              </div>
            ) : (
              <div className="space-y-2">
                {systemHealth.slice(0, 5).map((health, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 rounded hover:bg-muted/50"
                  >
                    <div className="flex items-center gap-2">
                      <div
                        className={`w-2 h-2 rounded-full ${getStatusColor(health.overall_status)}`}
                      />
                      <span className="text-sm">
                        {new Date(health.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      DB: {health.database_latency}ms
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Data Quality */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Data Quality Tier</CardTitle>
          </CardHeader>
          <CardContent>
            {dataQuality.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground text-sm">
                No recent quality checks
              </div>
            ) : (
              <div className="space-y-2">
                {dataQuality.slice(0, 5).map((quality, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 rounded hover:bg-muted/50"
                  >
                    <div className="flex items-center gap-2">
                      <div
                        className={`w-2 h-2 rounded-full ${getStatusColor(quality.overall_quality)}`}
                      />
                      <span className="text-sm">
                        {new Date(quality.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 text-xs">
                      <span>{quality.overall_score}/100</span>
                      {quality.remediation_triggered && (
                        <Badge variant="outline" className="text-xs">
                          Fixed
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Business Logic */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Business Logic Tier</CardTitle>
          </CardHeader>
          <CardContent>
            {businessLogic.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground text-sm">
                No recent business analysis
              </div>
            ) : (
              <div className="space-y-2">
                {businessLogic.slice(0, 5).map((business, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 rounded hover:bg-muted/50"
                  >
                    <div className="flex items-center gap-2">
                      <div
                        className={`w-2 h-2 rounded-full ${getStatusColor(business.business_health)}`}
                      />
                      <span className="text-sm">
                        {new Date(business.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Value: {business.business_value_score}/100
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Quick Navigation */}
      <Card>
        <CardHeader>
          <CardTitle>Monitoring Dashboards</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-3">
            <Button variant="outline" asChild className="h-auto p-4">
              <a
                href="/dashboard/system-health"
                className="flex flex-col items-center gap-2"
              >
                <CheckCircle className="h-6 w-6" />
                <div className="text-center">
                  <div className="font-medium">System Health</div>
                  <div className="text-xs text-muted-foreground">
                    Infrastructure monitoring
                  </div>
                </div>
              </a>
            </Button>

            <Button variant="outline" asChild className="h-auto p-4">
              <a
                href="/dashboard/data-quality"
                className="flex flex-col items-center gap-2"
              >
                <AlertTriangle className="h-6 w-6" />
                <div className="text-center">
                  <div className="font-medium">Data Quality</div>
                  <div className="text-xs text-muted-foreground">
                    AI-powered quality analysis
                  </div>
                </div>
              </a>
            </Button>

            <Button variant="outline" asChild className="h-auto p-4">
              <a
                href="/dashboard/business-logic"
                className="flex flex-col items-center gap-2"
              >
                <RefreshCw className="h-6 w-6" />
                <div className="text-center">
                  <div className="font-medium">Business Intelligence</div>
                  <div className="text-xs text-muted-foreground">
                    Strategic insights & analytics
                  </div>
                </div>
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>

      {overallStatus && (
        <div className="text-muted-foreground text-xs">
          Last coordinated check:{" "}
          {new Date(overallStatus.dashboard_generated_at).toLocaleString()}
        </div>
      )}
    </div>
  );
}
