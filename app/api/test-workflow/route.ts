import { openai } from '@ai-sdk/openai';
import { generateObject } from 'ai';
import { type NextRequest, NextResponse } from 'next/server';
import { JobExtractionSchema } from '@/lib/job-schema';
import { createClient } from '@/lib/supabase';
import { logger } from '@/lib/utils';

/**
 * Direct workflow test endpoint for development testing
 * This simulates the workflow logic without the Upstash Workflow wrapper
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    const body = await request.json();
    const { batchSize = 5 } = body;

    logger.info('🧪 Starting direct workflow test', { batchSize });

    const supabase = createClient();
    const workflowRunId = `test-${Date.now()}`;

    // Step 1: Lock jobs atomically
    logger.info('🔒 Locking jobs for processing');

    // First, get the job IDs to lock
    const { data: jobsToLock, error: fetchError } = await supabase
      .from('jobs')
      .select('id')
      .eq('processing_status', 'pending')
      .is('workflow_run_id', null)
      .order('created_at', { ascending: true })
      .limit(batchSize);

    if (fetchError) {
      logger.error('Failed to fetch jobs to lock', { error: fetchError });
      throw fetchError;
    }

    if (!jobsToLock || jobsToLock.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No jobs to process',
        processed: 0,
      });
    }

    const jobIds = jobsToLock.map((j) => j.id);

    // Now lock those specific jobs
    const { data: jobs, error: lockError } = await supabase
      .from('jobs')
      .update({
        processing_status: 'processing',
        workflow_run_id: workflowRunId,
        locked_at: new Date().toISOString(),
        locked_by: 'test-workflow',
      })
      .in('id', jobIds)
      .eq('processing_status', 'pending')
      .select('*');

    if (lockError) {
      logger.error('Failed to lock jobs', { error: lockError });
      throw lockError;
    }

    const lockedJobs = jobs || [];
    logger.info(`🔒 Locked ${lockedJobs.length} jobs`, {
      jobIds: lockedJobs.map((j) => j.id),
    });

    if (lockedJobs.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No jobs to process',
        processed: 0,
      });
    }

    // Record workflow run start
    await supabase.from('workflow_runs').insert({
      id: workflowRunId,
      workflow_type: 'test-process-jobs',
      status: 'running',
      job_count: lockedJobs.length,
    });

    // Step 2: Process jobs with AI
    logger.info(`🧠 Processing ${lockedJobs.length} jobs with AI`);

    const results = [];

    for (const job of lockedJobs) {
      try {
        logger.info(`Processing job ${job.id}`);

        const result = await generateObject({
          model: openai('gpt-4o-mini'),
          prompt: `Extract structured data from this job posting. Focus on key fields like title, company, location, salary, requirements, and benefits.\n\nJob Data:\n${JSON.stringify(job.raw_sourced_job_data)}`,
          schema: JobExtractionSchema,
        });

        results.push({
          id: job.id,
          success: true,
          ai_metadata: result.object,
          usage: result.usage,
        });

        logger.info(`✅ Successfully processed job ${job.id}`);
      } catch (error) {
        logger.error(`❌ Failed to process job ${job.id}`, { error });

        results.push({
          id: job.id,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    // Step 3: Save results
    logger.info('💾 Saving results to database');

    for (const result of results) {
      await supabase
        .from('jobs')
        .update({
          processing_status: result.success ? 'completed' : 'failed',
          ai_metadata: result.success ? result.ai_metadata : null,
          processed_at: new Date().toISOString(),
          // Clear workflow tracking
          workflow_run_id: null,
          locked_at: null,
          locked_by: null,
        })
        .eq('id', result.id);
    }

    const successCount = results.filter((r) => r.success).length;
    const failedCount = results.length - successCount;

    // Update workflow run
    await supabase
      .from('workflow_runs')
      .update({
        status: failedCount === 0 ? 'completed' : 'partial_failure',
        completed_at: new Date().toISOString(),
        error_message: failedCount > 0 ? `${failedCount} jobs failed` : null,
      })
      .eq('id', workflowRunId);

    const processingTime = Date.now() - startTime;

    logger.info('✅ Workflow test completed', {
      workflowRunId,
      processed: successCount,
      failed: failedCount,
      processingTime: `${processingTime}ms`,
    });

    return NextResponse.json({
      success: true,
      message: `Test workflow completed: ${successCount}/${lockedJobs.length} jobs processed`,
      workflowRunId,
      processed: successCount,
      failed: failedCount,
      processingTime: `${processingTime}ms`,
      results: results.map((r) => ({
        id: r.id,
        success: r.success,
        error: r.error,
      })),
    });
  } catch (error) {
    logger.error('Test workflow failed', { error });

    return NextResponse.json(
      {
        success: false,
        error: 'Test workflow failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    service: 'Workflow Test Endpoint',
    description:
      'Direct workflow testing (for local development)',
    usage: 'POST with {"batchSize": 5} to test job processing',
  });
}
