import { NextResponse } from "next/server";
import { handleApiError } from "@/lib/api-utils";
import {
  reconcileJobStatuses,
  getStatusInconsistencyStats,
} from "@/lib/status-reconciliation";
import { logger } from "@/lib/logger";

export async function POST() {
  try {
    logger.info("Starting job status reconciliation");

    const result = await reconcileJobStatuses();

    return NextResponse.json({
      success: true,
      message: `Reconciliation completed: ${result.fixed} jobs fixed`,
      result,
    });
  } catch (error) {
    logger.error("Job status reconciliation failed:", error);
    return handleApiError(error, "Job status reconciliation");
  }
}

export async function GET() {
  try {
    const stats = await getStatusInconsistencyStats();

    return NextResponse.json({
      success: true,
      stats,
      needsAction: stats.needsReconciliation,
    });
  } catch (error) {
    logger.error("Failed to get status inconsistency stats:", error);
    return handleApiError(error, "Status inconsistency check");
  }
}
