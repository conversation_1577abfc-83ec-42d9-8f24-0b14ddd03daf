import { serve } from "@upstash/workflow/nextjs";
import { createClient } from "@/lib/supabase";

interface TestPayload {
  testMessage: string;
  batchSize?: number;
}

export const { POST } = serve<TestPayload>(async (context) => {
  const { testMessage, batchSize = 1 } = context.requestPayload;

  console.log("🧪 Test job locking workflow started", {
    workflowRunId: context.workflowRunId,
    testMessage,
    batchSize,
  });

  // Step 1: Try to lock jobs with detailed logging
  const lockResult = await context.run("test-job-locking", async () => {
    console.log("🔹 Step 1: Testing job locking with simplified query");
    const supabase = createClient();
    
    try {
      // First, just SELECT jobs to see what's available
      const { data: availableJobs, error: selectError } = await supabase
        .from("jobs")
        .select("id, source_name, processing_status, workflow_run_id")
        .eq("processing_status", "pending")
        .is("workflow_run_id", null)
        .eq("source_name", "Workable")
        .order("created_at", { ascending: true })
        .limit(batchSize);

      if (selectError) {
        console.error("❌ SELECT error:", selectError);
        throw new Error(`SELECT failed: ${selectError.message}`);
      }

      console.log(`✅ Found ${availableJobs?.length || 0} available jobs:`, 
        availableJobs?.map(j => j.id));

      if (!availableJobs || availableJobs.length === 0) {
        return { 
          success: true, 
          message: "No jobs available for locking", 
          lockedJobs: [],
          availableJobs: 0 
        };
      }

      // Now try to UPDATE those specific jobs
      const jobIds = availableJobs.map(j => j.id);
      const { data: updatedJobs, error: updateError } = await supabase
        .from("jobs")
        .update({
          processing_status: "processing",
          workflow_run_id: context.workflowRunId,
          locked_at: new Date().toISOString(),
          locked_by: "test-workflow",
        })
        .in("id", jobIds)
        .eq("processing_status", "pending") // Double-check still pending
        .is("workflow_run_id", null) // Double-check not locked
        .select("id, source_name, processing_status, workflow_run_id");

      if (updateError) {
        console.error("❌ UPDATE error:", updateError);
        throw new Error(`UPDATE failed: ${updateError.message}`);
      }

      console.log(`✅ Successfully locked ${updatedJobs?.length || 0} jobs:`, 
        updatedJobs?.map(j => j.id));

      return { 
        success: true, 
        message: `Locked ${updatedJobs?.length || 0} jobs successfully`, 
        lockedJobs: updatedJobs || [],
        availableJobs: availableJobs.length,
        lockedCount: updatedJobs?.length || 0
      };

    } catch (error) {
      console.error("❌ Job locking failed:", error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : "Unknown error",
        lockedJobs: [],
        availableJobs: 0
      };
    }
  });

  // Step 2: Record test result in workflow run
  await context.run("record-test-result", async () => {
    console.log("🔹 Step 2: Recording test result");
    const supabase = createClient();
    
    const { error } = await supabase
      .from("workflow_runs")
      .insert({
        id: context.workflowRunId,
        workflow_type: "test-job-locking",
        status: lockResult.success ? "completed" : "failed",
        started_at: new Date().toISOString(),
        completed_at: new Date().toISOString(),
        job_count: lockResult.lockedCount || 0,
        error_message: lockResult.success ? null : lockResult.error,
      });

    if (error) {
      console.error("❌ Failed to record workflow run:", error);
      throw new Error(`Failed to record result: ${error.message}`);
    }

    console.log("✅ Test result recorded successfully");
    return { recorded: true };
  });

  // Step 3: Cleanup - unlock the jobs we locked for testing
  if (lockResult.success && lockResult.lockedJobs.length > 0) {
    await context.run("cleanup-test-locks", async () => {
      console.log("🔹 Step 3: Cleaning up test locks");
      const supabase = createClient();
      
      const jobIds = lockResult.lockedJobs.map((j: any) => j.id);
      const { error } = await supabase
        .from("jobs")
        .update({
          processing_status: "pending",
          workflow_run_id: null,
          locked_at: null,
          locked_by: null,
        })
        .in("id", jobIds);

      if (error) {
        console.error("❌ Failed to cleanup locks:", error);
        // Don't throw, just log - test was successful
      } else {
        console.log(`✅ Cleaned up ${jobIds.length} test locks`);
      }

      return { cleaned: jobIds.length };
    });
  }

  console.log("🎉 Test job locking workflow completed", {
    workflowRunId: context.workflowRunId,
    result: lockResult,
  });

  return {
    success: true,
    workflowRunId: context.workflowRunId,
    message: "Test job locking workflow completed",
    testResult: lockResult,
  };
});

export async function GET() {
  return Response.json({
    service: "Test Job Locking Workflow",
    status: "healthy",
    description: "Tests job locking mechanism with detailed logging and error handling",
  });
}
