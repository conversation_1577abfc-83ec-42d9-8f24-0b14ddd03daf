import { serve } from "@upstash/workflow/nextjs";
import { createClient } from "@/lib/supabase";
import { openai } from "@ai-sdk/openai";
import { generateObject } from "ai";
import { JobExtractionSchema } from "@/lib/job-schema";

interface TestAIPayload {
  testMessage: string;
  testJobId?: string;
}

export const { POST } = serve<TestAIPayload>(async (context) => {
  const { testMessage, testJobId } = context.requestPayload;

  console.log("🧪 AI Processing Test Workflow Started", {
    workflowRunId: context.workflowRunId,
    testMessage,
    testJobId,
  });

  // Record workflow start
  await context.run("record-workflow-start", async () => {
    const supabase = createClient();
    const { error } = await supabase.from("workflow_runs").insert({
      id: context.workflowRunId,
      type: "test-ai-processing",
      started_at: new Date().toISOString(),
      status: "running",
    });

    if (error) {
      console.error("❌ Failed to record workflow start:", error);
      throw error;
    }

    console.log("✅ Workflow start recorded");
    return { status: "workflow_recorded" };
  });

  // Step 1: Lock a single job for testing
  const lockResult = await context.run("lock-test-job", async () => {
    console.log("🔹 Step 1: Locking job for AI processing test");
    const supabase = createClient();

    try {
      // Use specific job ID if provided, otherwise find one
      let query = supabase
        .from("jobs")
        .select("id, source_name, raw_data, processing_status")
        .eq("processing_status", "pending")
        .is("workflow_run_id", null)
        .limit(1);

      if (testJobId) {
        query = query.eq("id", testJobId);
      } else {
        query = query.eq("source_name", "Workable");
      }

      const { data: availableJobs, error: selectError } = await query;

      if (selectError) {
        console.error("❌ Error selecting jobs:", selectError);
        throw selectError;
      }

      if (!availableJobs?.length) {
        console.log("⚠️ No jobs available for locking");
        return { success: false, reason: "no_jobs_available" };
      }

      const jobToLock = availableJobs[0];
      console.log("🎯 Locking job:", jobToLock.id);

      // Lock the job
      const { data: lockedJobs, error: updateError } = await supabase
        .from("jobs")
        .update({
          locked_at: new Date().toISOString(),
          locked_by: `test-ai-${context.workflowRunId}`,
          workflow_run_id: context.workflowRunId,
        })
        .eq("id", jobToLock.id)
        .eq("processing_status", "pending")
        .is("workflow_run_id", null)
        .select("*");

      if (updateError) {
        console.error("❌ Error locking job:", updateError);
        throw updateError;
      }

      if (!lockedJobs?.length) {
        console.log("⚠️ Job already locked by another process");
        return { success: false, reason: "job_already_locked" };
      }

      console.log("✅ Job locked successfully:", lockedJobs[0].id);
      return {
        success: true,
        job: lockedJobs[0],
        jobId: lockedJobs[0].id,
        rawData: lockedJobs[0].raw_data,
      };
    } catch (error) {
      console.error("❌ Critical error in job locking:", error);
      throw error;
    }
  });

  if (!lockResult.success) {
    console.log("🛑 Stopping workflow - no job to process");
    return {
      success: false,
      reason: lockResult.reason,
      workflowRunId: context.workflowRunId,
    };
  }

  // Step 2: Test AI Processing (This is where the real workflow probably fails)
  const aiResult = await context.run("test-ai-processing", async () => {
    console.log("🔹 Step 2: Testing AI processing with real job data");

    try {
      console.log("🤖 Starting extractJobData for job:", lockResult.jobId);
      console.log(
        "📄 Raw data preview:",
        JSON.stringify(lockResult.rawData).substring(0, 200) + "..."
      );

      // This is the actual AI processing call - using SAME pattern as working production workflow
      const result = await generateObject({
        model: openai("gpt-4o-mini"),
        prompt: `Extract structured data from this job posting. Focus on key fields like title, company, location, salary, requirements, and benefits.\n\nJob Data:\n${JSON.stringify(lockResult.rawData)}`,
        schema: JobExtractionSchema,
      });

      console.log("✅ AI processing completed successfully!");
      console.log("📊 Extracted data keys:", Object.keys(result.object || {}));
      console.log("🔧 Usage:", result.usage);

      return {
        success: true,
        extracted: result.object,
        usage: result.usage,
        originalJobId: lockResult.jobId,
      };
    } catch (error) {
      console.error("❌ AI processing failed:", error);
      const errorType =
        error instanceof Error ? error.constructor.name : "Unknown";
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error("❌ Error type:", errorType);
      console.error("❌ Error message:", errorMessage);

      // This is probably where the production workflow fails silently
      return {
        success: false,
        error: errorMessage,
        errorType: errorType,
        originalJobId: lockResult.jobId,
      };
    }
  });

  // Step 3: Update job with results (or clean up on failure)
  const updateResult = await context.run("update-job-results", async () => {
    console.log("🔹 Step 3: Updating job with AI processing results");
    const supabase = createClient();

    try {
      if (aiResult.success) {
        // Update job with extracted data
        const { error: updateError } = await supabase
          .from("jobs")
          .update({
            processing_status: "completed",
            extracted_data: aiResult.extracted,
            processed_at: new Date().toISOString(),
            locked_at: null,
            locked_by: null,
          })
          .eq("id", lockResult.jobId);

        if (updateError) {
          console.error("❌ Error updating job with results:", updateError);
          throw updateError;
        }

        console.log("✅ Job updated with AI results");
        return { success: true, jobProcessed: true };
      } else {
        // Clean up failed job
        const { error: cleanupError } = await supabase
          .from("jobs")
          .update({
            processing_status: "failed",
            error_message: aiResult.error,
            locked_at: null,
            locked_by: null,
            workflow_run_id: null,
          })
          .eq("id", lockResult.jobId);

        if (cleanupError) {
          console.error("❌ Error cleaning up failed job:", cleanupError);
          throw cleanupError;
        }

        console.log("🧹 Failed job cleaned up");
        return { success: true, jobProcessed: false, error: aiResult.error };
      }
    } catch (error) {
      console.error("❌ Critical error updating job:", error);
      throw error;
    }
  });

  // Step 4: Record workflow completion
  await context.run("record-workflow-completion", async () => {
    const supabase = createClient();
    const { error } = await supabase
      .from("workflow_runs")
      .update({
        status: "completed",
        completed_at: new Date().toISOString(),
        job_count: updateResult.jobProcessed ? 1 : 0,
        error_message: updateResult.error || null,
      })
      .eq("id", context.workflowRunId);

    if (error) {
      console.error("❌ Failed to record workflow completion:", error);
      throw error;
    }

    console.log("✅ Workflow completion recorded");
    return { status: "workflow_completed" };
  });

  console.log("🎉 AI Processing Test Workflow Completed", {
    workflowRunId: context.workflowRunId,
    jobProcessed: updateResult.jobProcessed,
    aiSuccess: aiResult.success,
    error: updateResult.error,
  });

  return {
    success: true,
    workflowRunId: context.workflowRunId,
    jobProcessed: updateResult.jobProcessed,
    aiProcessingSuccess: aiResult.success,
    error: updateResult.error,
  };
});
