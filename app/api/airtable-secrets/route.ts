import { NextResponse } from 'next/server';
import {
  listAvailablePats,
  migratePlainTextPats,
  storeEncryptedPat,
} from '@/lib/secrets-manager';
import { logger } from '@/lib/utils';

/**
 * GET /api/airtable-secrets
 * List available PAT configurations for all boards
 */
export async function GET() {
  try {
    const availablePats = await listAvailablePats();

    return NextResponse.json({
      success: true,
      availablePats,
      storageMethod: process.env.AIRTABLE_PAT_STORAGE_METHOD || 'encrypted_db',
    });
  } catch (error) {
    logger.error('Failed to list available PATs:', error);
    return NextResponse.json(
      { error: 'Failed to list available PATs' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/airtable-secrets
 * Store or update encrypted PAT for a board
 * Body: { boardId: string, pat: string }
 */
export async function POST(request: Request) {
  try {
    const { boardId, pat } = await request.json();

    if (!(boardId && pat)) {
      return NextResponse.json(
        { error: 'Board ID and PAT are required' },
        { status: 400 }
      );
    }

    // Basic PAT validation (should start with 'pat' and be reasonably long)
    if (!pat.startsWith('pat') || pat.length < 20) {
      return NextResponse.json(
        {
          error:
            'Invalid PAT format. PATs should start with "pat" and be at least 20 characters',
        },
        { status: 400 }
      );
    }

    await storeEncryptedPat(boardId, pat);

    logger.info(`Stored encrypted PAT for board: ${boardId}`);

    return NextResponse.json({
      success: true,
      message: 'PAT stored successfully',
      boardId,
    });
  } catch (error) {
    logger.error('Failed to store PAT:', error);
    return NextResponse.json({ error: 'Failed to store PAT' }, { status: 500 });
  }
}

/**
 * PUT /api/airtable-secrets/migrate
 * Migrate plain text PATs to encrypted storage
 * (Admin operation - should be protected in production)
 */
export async function PUT() {
  try {
    // Only allow in development or with admin key
    if (
      process.env.NODE_ENV === 'production' &&
      !process.env.ADMIN_SECRET_KEY
    ) {
      return NextResponse.json(
        {
          error:
            'Migration endpoint not available in production without admin key',
        },
        { status: 403 }
      );
    }

    await migratePlainTextPats();

    return NextResponse.json({
      success: true,
      message: 'Plain text PATs migrated to encrypted storage',
    });
  } catch (error) {
    logger.error('Failed to migrate PATs:', error);
    return NextResponse.json(
      { error: 'Failed to migrate PATs' },
      { status: 500 }
    );
  }
}
