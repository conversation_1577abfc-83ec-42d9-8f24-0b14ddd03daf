import { serve } from "@upstash/workflow/nextjs";
import { createClient } from "@/lib/supabase";

interface MinimalPayload {
  testMessage: string;
}

export const { POST } = serve<MinimalPayload>(async (context) => {
  console.log("🧪 Minimal workflow started", {
    workflowRunId: context.workflowRunId,
    payload: context.requestPayload,
  });

  // Step 1: Just log something
  const step1Result = await context.run("log-step", async () => {
    console.log("🔹 Step 1: Basic logging");
    return { message: "Step 1 completed", timestamp: new Date().toISOString() };
  });

  // Step 2: Try database connection
  const step2Result = await context.run("database-step", async () => {
    console.log("🔹 Step 2: Testing database connection");
    const supabase = createClient();
    
    // Simple database query
    const { data, error } = await supabase
      .from("jobs")
      .select("count", { count: "exact", head: true });
    
    if (error) {
      console.error("❌ Database error:", error);
      throw new Error(`Database connection failed: ${error.message}`);
    }
    
    console.log("✅ Database connection successful, job count:", data);
    return { 
      message: "Database connection successful", 
      jobCount: data,
      timestamp: new Date().toISOString() 
    };
  });

  // Step 3: Try to create a simple workflow run record
  const step3Result = await context.run("workflow-record-step", async () => {
    console.log("🔹 Step 3: Creating workflow run record");
    const supabase = createClient();
    
    const { data, error } = await supabase
      .from("workflow_runs")
      .insert({
        id: context.workflowRunId,
        workflow_type: "test-minimal",
        status: "completed",
        started_at: new Date().toISOString(),
        completed_at: new Date().toISOString(),
        job_count: 0,
      })
      .select()
      .single();
    
    if (error) {
      console.error("❌ Workflow record error:", error);
      throw new Error(`Failed to create workflow record: ${error.message}`);
    }
    
    console.log("✅ Workflow record created:", data);
    return { 
      message: "Workflow record created successfully", 
      record: data,
      timestamp: new Date().toISOString() 
    };
  });

  console.log("🎉 Minimal workflow completed successfully", {
    workflowRunId: context.workflowRunId,
    step1: step1Result,
    step2: step2Result,
    step3: step3Result,
  });

  return {
    success: true,
    workflowRunId: context.workflowRunId,
    message: "Minimal workflow completed successfully",
    steps: {
      step1: step1Result,
      step2: step2Result,
      step3: step3Result,
    },
  };
});

export async function GET() {
  return Response.json({
    service: "Minimal Workflow Test",
    status: "healthy",
    description: "Tests basic workflow execution, database connection, and record creation",
  });
}
