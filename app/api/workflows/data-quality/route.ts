import { serve } from "@upstash/workflow/nextjs";
import { NextResponse } from "next/server";
import { logger } from "@/lib/logger";
import { createServerClient } from "@/lib/supabase";
import { generateObject } from "ai";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";

// Data Quality Monitoring Payload
interface DataQualityPayload {
  datasetType?: "job_processing" | "manual_trigger";
  batchSize?: number;
  source?: string;
  actorRunId?: string;
}

// Apify Dataset Information (simplified)
interface ApifyDatasetInfo {
  itemCount: number;
  hasValidationErrors: boolean;
  sampleItems: any[];
  datasetHealth: {
    completeness: number;
    isEmpty: boolean;
    duplicateRate: number;
    nullFieldRate: number;
  };
  sourceMetrics: {
    totalJobs: number;
    processedJobs: number;
    pendingJobs: number;
    failedJobs: number;
  };
}

// AI Quality Analysis Schema
const DataQualityAnalysisSchema = z.object({
  overallQuality: z.enum(["EXCELLENT", "GOOD", "FAIR", "POOR", "CRITICAL"]),
  confidence: z.number().min(0).max(1),
  issues: z.array(
    z.object({
      type: z.enum([
        "missing_required_fields",
        "malformed_data",
        "duplicates",
        "inconsistent_formatting",
        "schema_validation_errors",
        "data_completeness",
        "ai_extraction_failures",
      ]),
      severity: z.enum(["low", "medium", "high", "critical"]),
      description: z.string(),
      affectedCount: z.number(),
      recommendation: z.string(),
    })
  ),
  metrics: z.object({
    dataCompleteness: z.number().min(0).max(1),
    schemaCompliance: z.number().min(0).max(1),
    dataConsistency: z.number().min(0).max(1),
    aiExtractionSuccess: z.number().min(0).max(1),
  }),
  recommendations: z.array(z.string()),
  actionPriority: z.enum([
    "none",
    "monitor",
    "investigate",
    "immediate_action",
  ]),
});

// Data Quality Report Interface
interface DataQualityReport {
  id: string;
  timestamp: string;
  dataset_type: string;
  total_items: number;
  apify_validation: ApifyDatasetInfo;
  ai_analysis: z.infer<typeof DataQualityAnalysisSchema>;
  actions_taken: string[];
  remediation_triggered: boolean;
  overall_score: number;
  recommendations: string[];
}

async function getJobDatasetAnalysis(): Promise<ApifyDatasetInfo> {
  try {
    const supabase = await createServerClient();

    // Get recent job processing statistics
    const { data: jobStats, error: jobStatsError } = await supabase
      .from("jobs")
      .select("processing_status, ai_metadata, created_at")
      .gte(
        "created_at",
        new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
      ) // Last 24 hours
      .limit(1000);

    if (jobStatsError) {
      logger.error("Failed to fetch job statistics", { error: jobStatsError });
      throw jobStatsError;
    }

    const totalJobs = jobStats?.length || 0;
    const processedJobs =
      jobStats?.filter((j) => j.processing_status === "completed").length || 0;
    const pendingJobs =
      jobStats?.filter((j) => j.processing_status === "pending").length || 0;
    const failedJobs =
      jobStats?.filter((j) => j.processing_status === "failed").length || 0;

    // Get sample of recent jobs for quality analysis
    const { data: sampleJobs, error: sampleError } = await supabase
      .from("jobs")
      .select("*")
      .eq("processing_status", "completed")
      .not("ai_metadata", "is", null)
      .order("processed_at", { ascending: false })
      .limit(20);

    if (sampleError) {
      logger.error("Failed to fetch sample jobs", { error: sampleError });
      throw sampleError;
    }

    // Analyze data quality metrics
    const sampleItems = sampleJobs || [];
    const itemCount = sampleItems.length;

    // Calculate quality metrics
    let nullFieldCount = 0;
    let duplicateCount = 0;
    const seenTitles = new Set();

    for (const job of sampleItems) {
      // Check for null/missing critical fields
      if (
        !job.title ||
        !job.company ||
        !job.ai_metadata?.title ||
        !job.ai_metadata?.company
      ) {
        nullFieldCount++;
      }

      // Check for duplicates based on title + company
      const key = `${job.title}-${job.company}`;
      if (seenTitles.has(key)) {
        duplicateCount++;
      } else {
        seenTitles.add(key);
      }
    }

    return {
      itemCount,
      hasValidationErrors: nullFieldCount > 0 || duplicateCount > 0,
      sampleItems: sampleItems.slice(0, 5), // Top 5 for AI analysis
      datasetHealth: {
        completeness: processedJobs / Math.max(totalJobs, 1),
        isEmpty: itemCount === 0,
        duplicateRate: duplicateCount / Math.max(itemCount, 1),
        nullFieldRate: nullFieldCount / Math.max(itemCount, 1),
      },
      sourceMetrics: {
        totalJobs,
        processedJobs,
        pendingJobs,
        failedJobs,
      },
    };
  } catch (error) {
    logger.error("Failed to analyze dataset", { error });
    throw error;
  }
}

async function performAIQualityAnalysis(
  datasetInfo: ApifyDatasetInfo
): Promise<z.infer<typeof DataQualityAnalysisSchema>> {
  try {
    if (datasetInfo.sampleItems.length === 0) {
      return {
        overallQuality: "CRITICAL",
        confidence: 1.0,
        issues: [
          {
            type: "data_completeness",
            severity: "critical",
            description: "No processed job data found",
            affectedCount: 0,
            recommendation: "Check job processing pipeline and AI extraction",
          },
        ],
        metrics: {
          dataCompleteness: 0,
          schemaCompliance: 0,
          dataConsistency: 0,
          aiExtractionSuccess: 0,
        },
        recommendations: [
          "Investigate job processing workflow",
          "Check AI extraction service status",
          "Verify database connectivity",
        ],
        actionPriority: "immediate_action",
      };
    }

    const prompt = `Analyze this job data for quality issues and provide detailed assessment:

DATASET METRICS:
- Total items: ${datasetInfo.itemCount}
- Completeness: ${(datasetInfo.datasetHealth.completeness * 100).toFixed(1)}%
- Duplicate rate: ${(datasetInfo.datasetHealth.duplicateRate * 100).toFixed(1)}%
- Null field rate: ${(datasetInfo.datasetHealth.nullFieldRate * 100).toFixed(1)}%

SOURCE METRICS:
- Total jobs (24h): ${datasetInfo.sourceMetrics.totalJobs}
- Processed: ${datasetInfo.sourceMetrics.processedJobs}
- Pending: ${datasetInfo.sourceMetrics.pendingJobs}
- Failed: ${datasetInfo.sourceMetrics.failedJobs}

SAMPLE JOB DATA:
${JSON.stringify(datasetInfo.sampleItems, null, 2)}

Analyze for:
1. Missing required fields (title, company, location, etc.)
2. Data format inconsistencies
3. AI extraction quality issues
4. Schema compliance problems
5. Data completeness and accuracy
6. Processing pipeline health

Provide specific, actionable recommendations for each issue found.`;

    const response = await generateObject({
      model: openai("gpt-4o-mini"),
      prompt,
      schema: DataQualityAnalysisSchema,
    });

    return response.object;
  } catch (error) {
    logger.error("AI quality analysis failed", { error });

    // Fallback analysis based on metrics
    const completeness = datasetInfo.datasetHealth.completeness;
    const duplicateRate = datasetInfo.datasetHealth.duplicateRate;
    const nullFieldRate = datasetInfo.datasetHealth.nullFieldRate;

    let overallQuality: "EXCELLENT" | "GOOD" | "FAIR" | "POOR" | "CRITICAL" =
      "GOOD";

    if (completeness < 0.5 || nullFieldRate > 0.3) {
      overallQuality = "CRITICAL";
    } else if (
      completeness < 0.8 ||
      nullFieldRate > 0.1 ||
      duplicateRate > 0.1
    ) {
      overallQuality = "POOR";
    } else if (completeness < 0.9 || nullFieldRate > 0.05) {
      overallQuality = "FAIR";
    } else if (
      completeness >= 0.95 &&
      nullFieldRate < 0.02 &&
      duplicateRate < 0.02
    ) {
      overallQuality = "EXCELLENT";
    }

    return {
      overallQuality,
      confidence: 0.7, // Lower confidence for fallback
      issues: [],
      metrics: {
        dataCompleteness: completeness,
        schemaCompliance: 1 - nullFieldRate,
        dataConsistency: 1 - duplicateRate,
        aiExtractionSuccess: completeness,
      },
      recommendations: ["AI analysis failed - manual review recommended"],
      actionPriority:
        overallQuality === "CRITICAL" ? "immediate_action" : "monitor",
    };
  }
}

async function triggerRemediation(
  analysisResult: z.infer<typeof DataQualityAnalysisSchema>
): Promise<string[]> {
  const actions: string[] = [];

  try {
    // Import here to avoid circular dependencies
    const { triggerStatusReconciliation } = await import(
      "@/lib/status-reconciliation"
    );
    const { triggerJobProcessing } = await import("@/lib/workflow-client");

    if (analysisResult.actionPriority === "immediate_action") {
      // Trigger status reconciliation for data inconsistencies
      try {
        await triggerStatusReconciliation();
        actions.push("status-reconciliation-triggered");
      } catch (error) {
        logger.error("Failed to trigger status reconciliation", { error });
      }

      // Re-process failed jobs if AI extraction issues detected
      const hasAIIssues = analysisResult.issues.some(
        (issue) =>
          issue.type === "ai_extraction_failures" ||
          issue.type === "schema_validation_errors"
      );

      if (hasAIIssues) {
        try {
          await triggerJobProcessing(5, "data-quality-remediation");
          actions.push("job-reprocessing-triggered");
        } catch (error) {
          logger.error("Failed to trigger job reprocessing", { error });
        }
      }
    }

    return actions;
  } catch (error) {
    logger.error("Failed to trigger remediation", { error });
    return actions;
  }
}

async function storeDataQualityReport(
  report: DataQualityReport
): Promise<void> {
  try {
    const supabase = await createServerClient();

    await supabase.from("data_quality_logs").insert({
      timestamp: report.timestamp,
      dataset_type: report.dataset_type,
      total_items: report.total_items,
      overall_score: report.overall_score,
      overall_quality: report.ai_analysis.overallQuality,
      confidence: report.ai_analysis.confidence,
      data_completeness: report.ai_analysis.metrics.dataCompleteness,
      schema_compliance: report.ai_analysis.metrics.schemaCompliance,
      data_consistency: report.ai_analysis.metrics.dataConsistency,
      ai_extraction_success: report.ai_analysis.metrics.aiExtractionSuccess,
      issues_count: report.ai_analysis.issues.length,
      critical_issues_count: report.ai_analysis.issues.filter(
        (i) => i.severity === "critical"
      ).length,
      actions_taken: report.actions_taken,
      remediation_triggered: report.remediation_triggered,
      raw_data: report,
    });

    logger.info("✅ Data quality report stored", {
      overallQuality: report.ai_analysis.overallQuality,
      score: report.overall_score,
    });
  } catch (error) {
    logger.error("❌ Failed to store data quality report", { error });
  }
}

async function sendSlackAlert(title: string, data: any): Promise<void> {
  const slackWebhookUrl = process.env.SLACK_WEBHOOK_URL;
  if (!slackWebhookUrl) return;

  try {
    await fetch(slackWebhookUrl, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        text: title,
        blocks: [
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*${title}*\n\`\`\`\n${JSON.stringify(data, null, 2)}\n\`\`\``,
            },
          },
        ],
      }),
    });
  } catch (error) {
    logger.error("Failed to send Slack alert", { title, error });
  }
}

async function triggerDataQualityMonitoring(
  payload?: DataQualityPayload
): Promise<string> {
  try {
    const response = await fetch(
      `${process.env.WORKFLOW_BASE_URL || "https://bordfeed.com"}/api/workflows/data-quality`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload || {}),
      }
    );

    if (!response.ok) {
      throw new Error(
        `Failed to trigger data quality monitoring: ${response.status}`
      );
    }

    const result = await response.json();
    return result.workflowRunId || "triggered";
  } catch (error) {
    logger.error("Failed to trigger data quality monitoring", { error });
    throw error;
  }
}

// Main Data Quality Monitoring Workflow
export const { POST } = serve<DataQualityPayload>(
  async (context) => {
    const payload = context.requestPayload || {};
    const datasetType = payload.datasetType || "job_processing";

    logger.info("📊 Starting data quality monitoring workflow", { payload });

    // Step 1: Analyze Current Dataset
    const datasetAnalysis = await context.run("dataset-analysis", async () => {
      logger.info("Analyzing job dataset quality...");
      return await getJobDatasetAnalysis();
    });

    // Step 2: AI-Powered Quality Analysis
    const aiAnalysis = await context.run("ai-quality-analysis", async () => {
      logger.info("Running AI quality analysis...");
      return await performAIQualityAnalysis(datasetAnalysis);
    });

    // Calculate overall score (0-100)
    const overallScore = Math.round(
      (aiAnalysis.metrics.dataCompleteness +
        aiAnalysis.metrics.schemaCompliance +
        aiAnalysis.metrics.dataConsistency +
        aiAnalysis.metrics.aiExtractionSuccess) *
        25
    );

    // Step 3: Trigger Remediation if Critical Issues Found
    let remediationActions: string[] = [];
    let remediationTriggered = false;

    if (
      aiAnalysis.actionPriority === "immediate_action" ||
      aiAnalysis.overallQuality === "CRITICAL"
    ) {
      remediationActions = await context.run(
        "trigger-remediation",
        async () => {
          logger.warn(
            "🚨 Critical data quality issues detected, triggering remediation",
            {
              quality: aiAnalysis.overallQuality,
              issues: aiAnalysis.issues.filter(
                (i) => i.severity === "critical"
              ),
            }
          );

          const actions = await triggerRemediation(aiAnalysis);
          return actions;
        }
      );

      remediationTriggered = remediationActions.length > 0;
    }

    // Step 4: Generate and Store Quality Report
    const qualityReport: DataQualityReport = {
      id: context.workflowRunId || `dq-${Date.now()}`,
      timestamp: new Date().toISOString(),
      dataset_type: datasetType,
      total_items: datasetAnalysis.itemCount,
      apify_validation: datasetAnalysis,
      ai_analysis: aiAnalysis,
      actions_taken: remediationActions,
      remediation_triggered: remediationTriggered,
      overall_score: overallScore,
      recommendations: aiAnalysis.recommendations,
    };

    await context.run("store-quality-report", async () => {
      await storeDataQualityReport(qualityReport);
    });

    // Step 5: Send Alerts for Critical Issues
    if (
      aiAnalysis.overallQuality === "CRITICAL" ||
      aiAnalysis.overallQuality === "POOR"
    ) {
      await context.run("send-quality-alert", async () => {
        logger.error("📊 Data quality alert triggered", {
          quality: aiAnalysis.overallQuality,
          score: overallScore,
        });

        await sendSlackAlert("📊 DATA QUALITY ALERT", {
          quality: aiAnalysis.overallQuality,
          score: `${overallScore}/100`,
          criticalIssues: aiAnalysis.issues.filter(
            (i) => i.severity === "critical"
          ).length,
          totalIssues: aiAnalysis.issues.length,
          dataset: {
            totalItems: datasetAnalysis.itemCount,
            completeness: `${(datasetAnalysis.datasetHealth.completeness * 100).toFixed(1)}%`,
            processing: `${datasetAnalysis.sourceMetrics.processedJobs}/${datasetAnalysis.sourceMetrics.totalJobs}`,
          },
          actions: remediationActions,
          recommendations: aiAnalysis.recommendations.slice(0, 3),
          timestamp: new Date().toISOString(),
        });
      });
    }

    // Step 6: Log Summary
    await context.run("log-summary", async () => {
      logger.info("📊 Data quality monitoring completed", {
        quality: aiAnalysis.overallQuality,
        score: overallScore,
        issues: aiAnalysis.issues.length,
        criticalIssues: aiAnalysis.issues.filter(
          (i) => i.severity === "critical"
        ).length,
        remediationTriggered,
        actions: remediationActions,
      });
    });

    return {
      success: true,
      quality: aiAnalysis.overallQuality,
      score: overallScore,
      issues: aiAnalysis.issues.length,
      remediationTriggered,
      actions: remediationActions,
      recommendations: aiAnalysis.recommendations,
    };
  },
  {
    retries: 2,
    failureFunction: async ({ context, failStatus }) => {
      logger.error("🚨 Data quality monitoring workflow failed", {
        failStatus,
        payload: context.requestPayload,
        timestamp: new Date().toISOString(),
      });

      await sendSlackAlert("💥 DATA QUALITY MONITORING FAILURE", {
        message: "Data quality monitoring workflow has failed",
        failStatus,
        payload: context.requestPayload,
        timestamp: new Date().toISOString(),
        action: "Manual data validation recommended",
      });
    },
  }
);

// Manual trigger endpoint
export async function GET() {
  try {
    const workflowRunId = await triggerDataQualityMonitoring({
      datasetType: "manual_trigger",
    });

    return NextResponse.json({
      success: true,
      message: "Data quality monitoring triggered",
      workflowRunId,
    });
  } catch (error) {
    logger.error("Failed to trigger data quality monitoring", { error });

    return NextResponse.json(
      {
        success: false,
        error: "Failed to trigger data quality monitoring",
        details: (error as Error).message,
      },
      { status: 500 }
    );
  }
}
