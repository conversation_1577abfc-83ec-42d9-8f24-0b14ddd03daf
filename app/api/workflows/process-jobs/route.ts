import { openai } from "@ai-sdk/openai";
import { serve } from "@upstash/workflow/nextjs";
import { generateObject, NoObjectGeneratedError } from "ai";
import { JobExtractionSchema } from "@/lib/job-schema";
import { JOB_EXTRACTION_PROMPT } from "@/lib/prompts";
import { replaceTemplateVariables } from "@/lib/api-utils";
import { createClient } from "@/lib/supabase";
import { logger } from "@/lib/utils";
import {
  createWorkflowRun,
  handleWorkflowError,
  lockJobsForProcessing,
  updateWorkflowRun,
} from "@/lib/workflow-utils";
import type {
  DatabaseJob,
  JobProcessingPayload,
  JobProcessingResult,
  WorkflowError,
} from "@/types/workflow";

export const { POST } = serve<JobProcessingPayload>(
  async (context) => {
    const { batchSize = 10, source } = context.requestPayload;
    const startTime = Date.now();

    logger.info("🚀 Starting workflow job processing", {
      workflowRunId: context.workflowRunId,
      requestPayload: {
        batchSize,
        source,
        timestamp: new Date().toISOString(),
      },
      environment: {
        modelUsed: "gpt-4o-mini",
        schemaVersion: "v5-optimized",
        nodeEnv: process.env.NODE_ENV,
        openaiKeyPresent: !!process.env.OPENAI_API_KEY,
        qstashUrlPresent: !!process.env.QSTASH_URL,
      },
    });

    // Step 1: Lock jobs atomically to prevent race conditions
    const { lockedJobs } = await context.run("lock-jobs", async () => {
      const lockResult = await lockJobsForProcessing(
        context.workflowRunId,
        batchSize,
        source
      );

      // Record workflow run start if jobs were locked
      if (lockResult.lockCount > 0) {
        await createWorkflowRun(
          context.workflowRunId,
          "process-jobs",
          lockResult.lockCount
        );
      }

      return lockResult;
    });

    if (lockedJobs.length === 0) {
      logger.info("No jobs to process");
      return {
        message: "No jobs to process",
        processed: 0,
        workflowRunId: context.workflowRunId,
      };
    }

    // Step 2: Process jobs with AI (parallel processing) with timeout protection
    const processedJobs = await context.run("ai-processing", async () => {
      logger.info(`🧠 Starting AI processing for ${lockedJobs.length} jobs`, {
        workflowRunId: context.workflowRunId,
        jobIds: lockedJobs.map((j) => j.id),
        modelUsed: "gpt-4o-mini",
        schemaVersion: "v5-optimized",
      });

      const results = await Promise.all(
        lockedJobs.map(async (job): Promise<JobProcessingResult> => {
          const jobStartTime = Date.now();

          logger.info(`🤖 Processing job ${job.id}`, {
            workflowRunId: context.workflowRunId,
            jobId: job.id,
            jobTitle: job.title || "Unknown",
            jobCompany: job.company || "Unknown",
            rawDataSize: JSON.stringify(job.raw_sourced_job_data || {}).length,
            sourceType: job.source_type,
            sourceName: job.source_name,
          });

          try {
            // Use the same sophisticated prompt as the extract endpoint
            const jobDataString = JSON.stringify(job.raw_sourced_job_data);
            const sophisticatedPrompt = replaceTemplateVariables(
              JOB_EXTRACTION_PROMPT,
              { content: jobDataString }
            );

            logger.debug(`🔍 AI extraction starting for job ${job.id}`, {
              workflowRunId: context.workflowRunId,
              jobId: job.id,
              promptLength: sophisticatedPrompt.length,
              model: "gpt-4o-mini",
              schemaFields: Object.keys(JobExtractionSchema.shape).length,
              usingPrompt: "JOB_EXTRACTION_PROMPT",
            });

            // Use AI SDK v5 generateObject with the same logic as extract endpoint
            const result = await generateObject({
              model: openai("gpt-4o-mini"),
              prompt: sophisticatedPrompt,
              schema: JobExtractionSchema,
            });

            const processingTime = Date.now() - jobStartTime;

            logger.info(`✅ Successfully processed job ${job.id}`, {
              workflowRunId: context.workflowRunId,
              jobId: job.id,
              processingTime: `${processingTime}ms`,
              finishReason: result.finishReason,
              usage: {
                inputTokens: result.usage?.inputTokens || 0,
                outputTokens: result.usage?.outputTokens || 0,
                totalTokens: result.usage?.totalTokens || 0,
              },
              extractedFields: {
                title: result.object?.title,
                company: result.object?.company,
                salaryMin: result.object?.salary_min,
                salaryMax: result.object?.salary_max,
                workplaceType: result.object?.workplace_type,
                location:
                  result.object?.workplace_city ||
                  result.object?.workplace_country,
              },
            });

            return {
              id: job.id,
              success: true,
              ai_metadata: result.object,
              usage: result.usage,
              processingTime,
              finishReason: result.finishReason,
            };
          } catch (error) {
            const processingTime = Date.now() - jobStartTime;

            // Handle specific AI SDK v5 errors with detailed logging
            if (NoObjectGeneratedError.isInstance(error)) {
              logger.error(`🤖 AI generation failed for job ${job.id}`, {
                workflowRunId: context.workflowRunId,
                jobId: job.id,
                processingTime: `${processingTime}ms`,
                errorType: "NoObjectGeneratedError",
                cause: error.cause,
                finishReason: error.finishReason,
                text:
                  error.text?.substring(0, 200) +
                  ((error.text?.length || 0) > 200 ? "..." : ""),
                usage: error.usage,
                response: {
                  id: error.response?.id,
                  modelId: error.response?.modelId,
                  timestamp: error.response?.timestamp,
                },
              });

              return {
                id: job.id,
                success: false,
                error: `AI generation failed: ${error.cause}`,
                errorType: "NoObjectGeneratedError",
                processingTime,
                finishReason: error.finishReason,
              };
            }

            // Handle other errors
            logger.error(`❌ Unexpected error processing job ${job.id}`, {
              workflowRunId: context.workflowRunId,
              jobId: job.id,
              processingTime: `${processingTime}ms`,
              errorType:
                error instanceof Error ? error.constructor.name : "Unknown",
              errorMessage:
                error instanceof Error ? error.message : "Unknown error",
              errorStack:
                error instanceof Error
                  ? error.stack?.substring(0, 500)
                  : undefined,
              jobData: {
                title: job.title,
                company: job.company,
                sourceType: job.source_type,
                rawDataSize: JSON.stringify(job.raw_sourced_job_data || {})
                  .length,
              },
            });

            return {
              id: job.id,
              success: false,
              error: error instanceof Error ? error.message : "Unknown error",
              errorType:
                error instanceof Error ? error.constructor.name : "Unknown",
              processingTime,
            };
          }
        })
      );

      const successCount = results.filter((r) => r.success).length;
      const failureCount = results.filter((r) => !r.success).length;
      const totalProcessingTime = results.reduce(
        (sum, r) => sum + (r.processingTime || 0),
        0
      );
      const averageProcessingTime =
        results.length > 0 ? totalProcessingTime / results.length : 0;

      // Detailed completion logging
      logger.info(`🧠 AI processing completed`, {
        workflowRunId: context.workflowRunId,
        summary: {
          total: lockedJobs.length,
          successful: successCount,
          failed: failureCount,
          successRate: `${((successCount / lockedJobs.length) * 100).toFixed(1)}%`,
        },
        performance: {
          totalProcessingTime: `${totalProcessingTime}ms`,
          averageProcessingTime: `${averageProcessingTime.toFixed(0)}ms`,
          totalTokensUsed: results.reduce(
            (sum, r) => sum + (r.usage?.totalTokens || 0),
            0
          ),
        },
        failures: results
          .filter((r) => !r.success)
          .map((r) => ({
            jobId: r.id,
            errorType: r.errorType,
            error: r.error,
          })),
      });

      return results;
    });

    // Step 3: Save results and unlock jobs
    await context.run("save-results", async () => {
      const supabase = createClient();

      logger.info("💾 Saving processing results to database");

      for (const result of processedJobs) {
        await supabase
          .from("jobs")
          .update({
            processing_status: result.success ? "completed" : "failed",
            ai_metadata: result.success ? result.ai_metadata : null,
            processed_at: new Date().toISOString(),
            // Clear workflow tracking fields
            workflow_run_id: null,
            locked_at: null,
            locked_by: null,
          })
          .eq("id", result.id);
      }

      // Update workflow run status
      const successCount = processedJobs.filter((j) => j.success).length;
      const failedCount = processedJobs.length - successCount;

      await updateWorkflowRun(
        context.workflowRunId,
        failedCount === 0 ? "completed" : "partial_failure",
        failedCount > 0 ? `${failedCount} jobs failed processing` : undefined
      );

      logger.info("💾 Results saved successfully", {
        successful: successCount,
        failed: failedCount,
      });
    });

    const successCount = processedJobs.filter((j) => j.success).length;
    const failedCount = processedJobs.length - successCount;
    const totalDuration = Date.now() - startTime;

    // Comprehensive workflow completion logging
    logger.info("🏁 Workflow completed", {
      workflowRunId: context.workflowRunId,
      summary: {
        totalJobs: lockedJobs.length,
        successful: successCount,
        failed: failedCount,
        successRate: `${((successCount / lockedJobs.length) * 100).toFixed(1)}%`,
        source: source || "all_sources",
      },
      performance: {
        totalDuration: `${totalDuration}ms`,
        averageTimePerJob:
          lockedJobs.length > 0
            ? `${(totalDuration / lockedJobs.length).toFixed(0)}ms`
            : "0ms",
        throughput: `${((lockedJobs.length / (totalDuration / 1000)) * 60).toFixed(1)} jobs/minute`,
      },
      details: {
        jobIds: processedJobs.map((j) => j.id),
        failureDetails: processedJobs
          .filter((j) => !j.success)
          .map((j) => ({
            jobId: j.id,
            errorType: j.errorType,
            error: j.error,
            processingTime: j.processingTime,
          })),
      },
      timestamp: new Date().toISOString(),
    });

    // Log pipeline completion
    logger.pipeline({
      step: "PROCESSED",
      source: source || "unknown",
      jobCount: successCount,
      success: failedCount === 0,
      batchId: context.workflowRunId,
      duration: totalDuration,
      metadata: {
        workflowRunId: context.workflowRunId,
        totalJobs: lockedJobs.length,
        successful: successCount,
        failed: failedCount,
        aiModelUsed: "gpt-4o-mini",
        schemaVersion: "v5-optimized",
      },
    });

    // NEW: Trigger data quality monitoring after successful job processing
    if (successCount > 0) {
      await context.run("trigger-data-quality-monitoring", async () => {
        try {
          const { triggerDataQualityMonitoring } = await import(
            "@/lib/workflow-client"
          );
          await triggerDataQualityMonitoring({
            datasetType: "job_processing",
            source: `job-processing-batch-${context.workflowRunId}`,
          });
          logger.info(
            "✅ Data quality monitoring triggered after job processing",
            {
              workflowRunId: context.workflowRunId,
              successCount,
            }
          );
        } catch (error) {
          logger.error("❌ Failed to trigger data quality monitoring", {
            error,
          });
          // Don't fail the main workflow if monitoring fails
        }
      });
    }

    return {
      message: `Processed ${successCount}/${lockedJobs.length} jobs successfully`,
      processed: successCount,
      failed: failedCount,
      workflowRunId: context.workflowRunId,
      jobIds: processedJobs.map((j) => j.id),
      duration: totalDuration,
      performance: {
        successRate:
          ((successCount / lockedJobs.length) * 100).toFixed(1) + "%",
        averageTimePerJob:
          lockedJobs.length > 0
            ? (totalDuration / lockedJobs.length).toFixed(0) + "ms"
            : "0ms",
      },
    };
  },
  {
    // Configure retry behavior to prevent infinite loops
    retries: 2, // Reduce retries from default 3 to 2

    // Add failure handling
    failureFunction: async ({ context, failStatus, failResponse }) => {
      logger.error("🚨 Workflow failed after all retries", {
        workflowRunId: context.workflowRunId,
        failStatus,
        failResponse: failResponse ? String(failResponse) : "No response",
        timestamp: new Date().toISOString(),
      });
    },
  }
);

// Health check endpoint
export async function GET() {
  return Response.json({
    service: "Upstash Workflow Job Processor",
    status: "healthy",
    features: [
      "Atomic job locking (prevents race conditions)",
      "Parallel AI processing with GPT-4o-mini",
      "Automatic error handling and recovery",
      "Pipeline metrics tracking",
      "Workflow execution logging",
    ],
    environment: {
      hasOpenAI: !!process.env.OPENAI_API_KEY,
      hasSupabase: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasWorkflow: !!process.env.QSTASH_TOKEN, // Workflow client token
    },
  });
}
