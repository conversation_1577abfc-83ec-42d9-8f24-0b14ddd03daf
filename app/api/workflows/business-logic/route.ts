import { serve } from "@upstash/workflow/nextjs";
import { NextResponse } from "next/server";
import { logger } from "@/lib/logger";
import { createServerClient } from "@/lib/supabase";
import { generateObject } from "ai";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";

// Business Logic Monitoring Payload
interface BusinessLogicPayload {
  monitoringType?: "performance" | "kpis" | "predictions" | "comprehensive";
  timeframe?: "1h" | "24h" | "7d" | "30d";
  includeForecasting?: boolean;
  triggerSource?: string;
}

// Business KPI Metrics
interface BusinessKPIs {
  jobProcessing: {
    totalJobsProcessed: number;
    processingSuccessRate: number;
    averageProcessingTime: number;
    aiExtractionAccuracy: number;
    dailyProcessingVolume: number;
    peakProcessingHours: string[];
  };
  dataQuality: {
    overallQualityScore: number;
    criticalIssuesCount: number;
    dataCompletenessRate: number;
    schemaComplianceRate: number;
    duplicateRate: number;
  };
  systemHealth: {
    uptimePercentage: number;
    averageResponseTime: number;
    errorRate: number;
    serviceAvailability: number;
  };
  businessMetrics: {
    activeJobSources: number;
    uniqueCompanies: number;
    jobTypesDistribution: Record<string, number>;
    geographicCoverage: number;
    freshJobsPercentage: number;
  };
}

// AI Insights Schema
const BusinessInsightsSchema = z.object({
  overallBusinessHealth: z.enum([
    "EXCELLENT",
    "GOOD",
    "FAIR",
    "CONCERNING",
    "CRITICAL",
  ]),
  confidence: z.number().min(0).max(1),
  keyInsights: z.array(
    z.object({
      category: z.enum([
        "performance_optimization",
        "data_quality_trends",
        "business_growth",
        "operational_efficiency",
        "risk_assessment",
        "cost_optimization",
      ]),
      insight: z.string(),
      impact: z.enum(["high", "medium", "low"]),
      priority: z.enum(["immediate", "short_term", "long_term"]),
      actionable: z.boolean(),
    })
  ),
  predictions: z.object({
    next24hJobVolume: z.number(),
    expectedProcessingTime: z.number(),
    qualityTrend: z.enum(["improving", "stable", "declining"]),
    resourceUtilization: z.number().min(0).max(100),
    riskFactors: z.array(z.string()),
  }),
  recommendations: z.array(
    z.object({
      category: z.string(),
      recommendation: z.string(),
      expectedImpact: z.string(),
      implementationEffort: z.enum(["low", "medium", "high"]),
      roi_estimate: z.enum(["high", "medium", "low"]),
    })
  ),
  performanceMetrics: z.object({
    efficiencyScore: z.number().min(0).max(100),
    scalabilityIndex: z.number().min(0).max(100),
    reliabilityScore: z.number().min(0).max(100),
    innovationIndex: z.number().min(0).max(100),
  }),
  alertLevel: z.enum(["none", "info", "warning", "critical"]),
});

// Business Logic Report Interface
interface BusinessLogicReport {
  id: string;
  timestamp: string;
  monitoring_type: string;
  timeframe: string;
  business_kpis: BusinessKPIs;
  ai_insights: z.infer<typeof BusinessInsightsSchema>;
  actions_recommended: string[];
  executive_summary: string;
  cost_optimization_potential: number;
  business_value_score: number;
}

async function collectBusinessKPIs(timeframe: string): Promise<BusinessKPIs> {
  try {
    const supabase = await createServerClient();

    // Calculate time range
    const timeRangeHours =
      {
        "1h": 1,
        "24h": 24,
        "7d": 168,
        "30d": 720,
      }[timeframe] || 24;

    const startTime = new Date(
      Date.now() - timeRangeHours * 60 * 60 * 1000
    ).toISOString();

    // Job Processing Metrics
    const { data: jobMetrics, error: jobError } = await supabase
      .from("jobs")
      .select(
        "processing_status, ai_metadata, created_at, processed_at, updated_at"
      )
      .gte("created_at", startTime);

    if (jobError) throw jobError;

    const totalJobs = jobMetrics?.length || 0;
    const completedJobs =
      jobMetrics?.filter((j) => j.processing_status === "completed").length ||
      0;
    const successRate = totalJobs > 0 ? completedJobs / totalJobs : 0;

    // Calculate average processing time
    const processingTimes =
      jobMetrics
        ?.filter((j) => j.processed_at && j.created_at)
        .map(
          (j) =>
            new Date(j.processed_at).getTime() -
            new Date(j.created_at).getTime()
        ) || [];

    const avgProcessingTime =
      processingTimes.length > 0
        ? processingTimes.reduce((a, b) => a + b, 0) /
          processingTimes.length /
          1000 // Convert to seconds
        : 0;

    // AI Extraction Accuracy
    const aiSuccessfulJobs =
      jobMetrics?.filter(
        (j) => j.ai_metadata && Object.keys(j.ai_metadata).length > 0
      ).length || 0;
    const aiAccuracy = totalJobs > 0 ? aiSuccessfulJobs / totalJobs : 0;

    // Daily processing volume (jobs per day)
    const dailyVolume = totalJobs / Math.max(timeRangeHours / 24, 1);

    // Peak processing hours analysis
    const hourlyDistribution =
      jobMetrics?.reduce(
        (acc, job) => {
          const hour = new Date(job.created_at).getHours();
          acc[hour] = (acc[hour] || 0) + 1;
          return acc;
        },
        {} as Record<number, number>
      ) || {};

    const sortedHours = Object.entries(hourlyDistribution)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .map(([hour]) => `${hour}:00`);

    // Data Quality Metrics
    const { data: qualityMetrics } = (await supabase
      .from("data_quality_logs")
      .select(
        "overall_score, critical_issues_count, data_completeness, schema_compliance"
      )
      .gte("timestamp", startTime)
      .order("timestamp", { ascending: false })
      .limit(10)) || { data: [] };

    const latestQuality = qualityMetrics?.[0];
    const avgQualityScore =
      qualityMetrics && qualityMetrics.length > 0
        ? qualityMetrics.reduce((sum, q) => sum + q.overall_score, 0) /
          qualityMetrics.length
        : 0;

    // System Health Metrics
    const { data: healthMetrics } = (await supabase
      .from("system_health_logs")
      .select(
        "overall_status, database_latency, workflow_latency, apify_latency"
      )
      .gte("timestamp", startTime)
      .order("timestamp", { ascending: false })
      .limit(100)) || { data: [] };

    const healthyChecks =
      healthMetrics?.filter((h) => h.overall_status === "healthy").length || 0;
    const uptimePercentage =
      healthMetrics && healthMetrics.length > 0
        ? healthyChecks / healthMetrics.length
        : 0;

    const avgResponseTime =
      healthMetrics && healthMetrics.length > 0
        ? healthMetrics.reduce(
            (sum, h) =>
              sum + (h.database_latency + h.workflow_latency + h.apify_latency),
            0
          ) /
          (healthMetrics.length * 3)
        : 0;

    const errorRate =
      healthMetrics && healthMetrics.length > 0 ? 1 - uptimePercentage : 0;

    // Business Metrics
    const { data: companyStats } = (await supabase
      .from("jobs")
      .select("company, type, location")
      .gte("created_at", startTime)) || { data: [] };

    const uniqueCompanies = new Set(
      companyStats?.map((j) => j.company).filter(Boolean)
    ).size;
    const jobTypesDistribution =
      companyStats?.reduce(
        (acc, job) => {
          if (job.type) {
            acc[job.type] = (acc[job.type] || 0) + 1;
          }
          return acc;
        },
        {} as Record<string, number>
      ) || {};

    const uniqueLocations = new Set(
      companyStats?.map((j) => j.location).filter(Boolean)
    ).size;

    // Fresh jobs percentage (jobs less than 7 days old)
    const sevenDaysAgo = new Date(
      Date.now() - 7 * 24 * 60 * 60 * 1000
    ).toISOString();
    const { count: freshJobsCount } = (await supabase
      .from("jobs")
      .select("*", { count: "exact", head: true })
      .gte("created_at", sevenDaysAgo)) || { count: 0 };

    const { count: totalJobsCount } = (await supabase
      .from("jobs")
      .select("*", { count: "exact", head: true })) || { count: 0 };

    const freshJobsPercentage =
      totalJobsCount && totalJobsCount > 0
        ? (freshJobsCount || 0) / totalJobsCount
        : 0;

    // Get active sources count
    const { data: sourcesData } = (await supabase
      .from("sources")
      .select("id, is_active")
      .eq("is_active", true)) || { data: [] };

    return {
      jobProcessing: {
        totalJobsProcessed: totalJobs,
        processingSuccessRate: successRate,
        averageProcessingTime: Math.round(avgProcessingTime),
        aiExtractionAccuracy: aiAccuracy,
        dailyProcessingVolume: Math.round(dailyVolume),
        peakProcessingHours: sortedHours,
      },
      dataQuality: {
        overallQualityScore: Math.round(avgQualityScore),
        criticalIssuesCount: latestQuality?.critical_issues_count || 0,
        dataCompletenessRate: latestQuality?.data_completeness || 0,
        schemaComplianceRate: latestQuality?.schema_compliance || 0,
        duplicateRate: 0.02, // Placeholder - would calculate from actual data
      },
      systemHealth: {
        uptimePercentage: uptimePercentage,
        averageResponseTime: Math.round(avgResponseTime),
        errorRate: errorRate,
        serviceAvailability: uptimePercentage,
      },
      businessMetrics: {
        activeJobSources: sourcesData?.length || 0,
        uniqueCompanies: uniqueCompanies,
        jobTypesDistribution: jobTypesDistribution,
        geographicCoverage: uniqueLocations,
        freshJobsPercentage: freshJobsPercentage,
      },
    };
  } catch (error) {
    logger.error("Failed to collect business KPIs", { error });
    throw error;
  }
}

async function generateBusinessInsights(
  kpis: BusinessKPIs,
  timeframe: string
): Promise<z.infer<typeof BusinessInsightsSchema>> {
  try {
    const prompt = `Analyze this bordfeed.com job board business data and provide strategic insights:

TIMEFRAME: ${timeframe}

JOB PROCESSING METRICS:
- Total Jobs Processed: ${kpis.jobProcessing.totalJobsProcessed}
- Success Rate: ${(kpis.jobProcessing.processingSuccessRate * 100).toFixed(1)}%
- Avg Processing Time: ${kpis.jobProcessing.averageProcessingTime}s
- AI Extraction Accuracy: ${(kpis.jobProcessing.aiExtractionAccuracy * 100).toFixed(1)}%
- Daily Volume: ${kpis.jobProcessing.dailyProcessingVolume} jobs/day
- Peak Hours: ${kpis.jobProcessing.peakProcessingHours.join(", ")}

DATA QUALITY METRICS:
- Overall Quality Score: ${kpis.dataQuality.overallQualityScore}/100
- Critical Issues: ${kpis.dataQuality.criticalIssuesCount}
- Data Completeness: ${(kpis.dataQuality.dataCompletenessRate * 100).toFixed(1)}%
- Schema Compliance: ${(kpis.dataQuality.schemaComplianceRate * 100).toFixed(1)}%
- Duplicate Rate: ${(kpis.dataQuality.duplicateRate * 100).toFixed(1)}%

SYSTEM HEALTH METRICS:
- Uptime: ${(kpis.systemHealth.uptimePercentage * 100).toFixed(1)}%
- Avg Response Time: ${kpis.systemHealth.averageResponseTime}ms
- Error Rate: ${(kpis.systemHealth.errorRate * 100).toFixed(1)}%
- Service Availability: ${(kpis.systemHealth.serviceAvailability * 100).toFixed(1)}%

BUSINESS METRICS:
- Active Job Sources: ${kpis.businessMetrics.activeJobSources}
- Unique Companies: ${kpis.businessMetrics.uniqueCompanies}
- Geographic Coverage: ${kpis.businessMetrics.geographicCoverage} locations
- Fresh Jobs Rate: ${(kpis.businessMetrics.freshJobsPercentage * 100).toFixed(1)}%
- Job Types: ${Object.entries(kpis.businessMetrics.jobTypesDistribution)
      .map(([type, count]) => `${type}: ${count}`)
      .join(", ")}

Provide:
1. Strategic business insights with impact assessment
2. Performance optimization opportunities
3. Predictive analytics for next 24h
4. Risk assessment and mitigation strategies
5. Cost optimization recommendations
6. Revenue growth opportunities
7. Operational efficiency improvements

Focus on actionable insights that drive business value and competitive advantage.`;

    const response = await generateObject({
      model: openai("gpt-4o-mini"),
      prompt,
      schema: BusinessInsightsSchema,
    });

    return response.object;
  } catch (error) {
    logger.error("AI business insights analysis failed", { error });

    // Fallback business insights based on metrics
    const overallScore =
      kpis.jobProcessing.processingSuccessRate * 30 +
      (kpis.dataQuality.overallQualityScore / 100) * 25 +
      kpis.systemHealth.uptimePercentage * 25 +
      kpis.businessMetrics.freshJobsPercentage * 20;

    let businessHealth:
      | "EXCELLENT"
      | "GOOD"
      | "FAIR"
      | "CONCERNING"
      | "CRITICAL" = "GOOD";

    if (overallScore >= 0.9) businessHealth = "EXCELLENT";
    else if (overallScore >= 0.75) businessHealth = "GOOD";
    else if (overallScore >= 0.6) businessHealth = "FAIR";
    else if (overallScore >= 0.4) businessHealth = "CONCERNING";
    else businessHealth = "CRITICAL";

    return {
      overallBusinessHealth: businessHealth,
      confidence: 0.7,
      keyInsights: [
        {
          category: "performance_optimization",
          insight: `Processing success rate at ${(kpis.jobProcessing.processingSuccessRate * 100).toFixed(1)}% - ${kpis.jobProcessing.processingSuccessRate < 0.9 ? "optimization needed" : "performing well"}`,
          impact:
            kpis.jobProcessing.processingSuccessRate < 0.8 ? "high" : "medium",
          priority:
            kpis.jobProcessing.processingSuccessRate < 0.8
              ? "immediate"
              : "short_term",
          actionable: true,
        },
      ],
      predictions: {
        next24hJobVolume: Math.round(
          kpis.jobProcessing.dailyProcessingVolume * 1.1
        ),
        expectedProcessingTime: kpis.jobProcessing.averageProcessingTime,
        qualityTrend:
          kpis.dataQuality.overallQualityScore > 80 ? "stable" : "declining",
        resourceUtilization: Math.min(
          95,
          (kpis.jobProcessing.dailyProcessingVolume / 100) * 100
        ),
        riskFactors:
          kpis.systemHealth.errorRate > 0.05
            ? ["High error rate detected"]
            : [],
      },
      recommendations: [
        {
          category: "Operational Efficiency",
          recommendation:
            "AI analysis failed - implement manual KPI review process",
          expectedImpact: "Maintain business intelligence capabilities",
          implementationEffort: "low",
          roi_estimate: "medium",
        },
      ],
      performanceMetrics: {
        efficiencyScore: Math.round(
          kpis.jobProcessing.processingSuccessRate * 100
        ),
        scalabilityIndex: Math.round(kpis.systemHealth.uptimePercentage * 100),
        reliabilityScore: Math.round((1 - kpis.systemHealth.errorRate) * 100),
        innovationIndex: Math.round(
          kpis.jobProcessing.aiExtractionAccuracy * 100
        ),
      },
      alertLevel:
        businessHealth === "CRITICAL" || businessHealth === "CONCERNING"
          ? "critical"
          : "info",
    };
  }
}

async function storeBusinessLogicReport(
  report: BusinessLogicReport
): Promise<void> {
  try {
    const supabase = await createServerClient();

    await supabase.from("business_logic_logs").insert({
      timestamp: report.timestamp,
      monitoring_type: report.monitoring_type,
      timeframe: report.timeframe,
      business_health: report.ai_insights.overallBusinessHealth,
      confidence: report.ai_insights.confidence,
      efficiency_score: report.ai_insights.performanceMetrics.efficiencyScore,
      scalability_index: report.ai_insights.performanceMetrics.scalabilityIndex,
      reliability_score: report.ai_insights.performanceMetrics.reliabilityScore,
      innovation_index: report.ai_insights.performanceMetrics.innovationIndex,
      predicted_job_volume: report.ai_insights.predictions.next24hJobVolume,
      cost_optimization_potential: report.cost_optimization_potential,
      business_value_score: report.business_value_score,
      insights_count: report.ai_insights.keyInsights.length,
      recommendations_count: report.ai_insights.recommendations.length,
      alert_level: report.ai_insights.alertLevel,
      actions_recommended: report.actions_recommended,
      executive_summary: report.executive_summary,
      raw_data: report,
    });

    logger.info("✅ Business logic report stored", {
      businessHealth: report.ai_insights.overallBusinessHealth,
      valueScore: report.business_value_score,
    });
  } catch (error) {
    logger.error("❌ Failed to store business logic report", { error });
  }
}

async function sendSlackAlert(title: string, data: any): Promise<void> {
  const slackWebhookUrl = process.env.SLACK_WEBHOOK_URL;
  if (!slackWebhookUrl) return;

  try {
    await fetch(slackWebhookUrl, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        text: title,
        blocks: [
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*${title}*\n\`\`\`\n${JSON.stringify(data, null, 2)}\n\`\`\``,
            },
          },
        ],
      }),
    });
  } catch (error) {
    logger.error("Failed to send Slack alert", { title, error });
  }
}

async function triggerBusinessLogicMonitoring(
  payload?: BusinessLogicPayload
): Promise<string> {
  try {
    const response = await fetch(
      `${process.env.WORKFLOW_BASE_URL || "https://bordfeed.com"}/api/workflows/business-logic`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload || {}),
      }
    );

    if (!response.ok) {
      throw new Error(
        `Failed to trigger business logic monitoring: ${response.status}`
      );
    }

    const result = await response.json();
    return result.workflowRunId || "triggered";
  } catch (error) {
    logger.error("Failed to trigger business logic monitoring", { error });
    throw error;
  }
}

// Main Business Logic Monitoring Workflow
export const { POST } = serve<BusinessLogicPayload>(
  async (context) => {
    const payload = context.requestPayload || {};
    const monitoringType = payload.monitoringType || "comprehensive";
    const timeframe = payload.timeframe || "24h";
    const includeForecasting = payload.includeForecasting !== false;

    logger.info("📈 Starting business logic monitoring workflow", { payload });

    // Step 1: Collect Business KPIs
    const businessKPIs = await context.run(
      "collect-business-kpis",
      async () => {
        logger.info("Collecting comprehensive business KPIs...");
        return await collectBusinessKPIs(timeframe);
      }
    );

    // Step 2: Generate AI-Powered Business Insights
    const aiInsights = await context.run(
      "generate-business-insights",
      async () => {
        logger.info("Generating AI-powered business insights...");
        return await generateBusinessInsights(businessKPIs, timeframe);
      }
    );

    // Step 3: Calculate Business Value Score
    const businessValueScore = await context.run(
      "calculate-business-value",
      async () => {
        const valueScore = Math.round(
          aiInsights.performanceMetrics.efficiencyScore * 0.3 +
            aiInsights.performanceMetrics.scalabilityIndex * 0.25 +
            aiInsights.performanceMetrics.reliabilityScore * 0.25 +
            aiInsights.performanceMetrics.innovationIndex * 0.2
        );

        logger.info("Business value score calculated", { valueScore });
        return valueScore;
      }
    );

    // Step 4: Calculate Cost Optimization Potential
    const costOptimizationPotential = await context.run(
      "calculate-cost-optimization",
      async () => {
        // Calculate based on efficiency metrics and recommendations
        const highImpactRecs = aiInsights.recommendations.filter(
          (r) => r.roi_estimate === "high" && r.implementationEffort !== "high"
        ).length;

        const optimizationPotential = Math.min(30, highImpactRecs * 5); // Max 30% potential
        logger.info("Cost optimization potential calculated", {
          optimizationPotential,
        });
        return optimizationPotential;
      }
    );

    // Step 5: Generate Executive Summary
    const executiveSummary = await context.run(
      "generate-executive-summary",
      async () => {
        return (
          `Business Health: ${aiInsights.overallBusinessHealth} | ` +
          `Efficiency: ${aiInsights.performanceMetrics.efficiencyScore}% | ` +
          `Key Insights: ${aiInsights.keyInsights.length} | ` +
          `Recommendations: ${aiInsights.recommendations.length} | ` +
          `Predicted Volume: ${aiInsights.predictions.next24hJobVolume} jobs/24h | ` +
          `Cost Optimization: ${costOptimizationPotential}% potential`
        );
      }
    );

    // Step 6: Determine Recommended Actions
    const actionsRecommended = await context.run(
      "determine-actions",
      async () => {
        const actions: string[] = [];

        if (aiInsights.alertLevel === "critical") {
          actions.push("immediate-intervention-required");
        }

        if (aiInsights.performanceMetrics.efficiencyScore < 70) {
          actions.push("performance-optimization-needed");
        }

        if (aiInsights.predictions.riskFactors.length > 0) {
          actions.push("risk-mitigation-planning");
        }

        if (costOptimizationPotential > 15) {
          actions.push("cost-optimization-implementation");
        }

        return actions;
      }
    );

    // Step 7: Generate and Store Business Logic Report
    const businessReport: BusinessLogicReport = {
      id: context.workflowRunId || `bl-${Date.now()}`,
      timestamp: new Date().toISOString(),
      monitoring_type: monitoringType,
      timeframe: timeframe,
      business_kpis: businessKPIs,
      ai_insights: aiInsights,
      actions_recommended: actionsRecommended,
      executive_summary: executiveSummary,
      cost_optimization_potential: costOptimizationPotential,
      business_value_score: businessValueScore,
    };

    await context.run("store-business-report", async () => {
      await storeBusinessLogicReport(businessReport);
    });

    // Step 8: Send Strategic Alerts
    if (
      aiInsights.alertLevel === "critical" ||
      aiInsights.overallBusinessHealth === "CRITICAL"
    ) {
      await context.run("send-strategic-alert", async () => {
        logger.error("📈 Critical business metrics detected", {
          businessHealth: aiInsights.overallBusinessHealth,
          valueScore: businessValueScore,
        });

        await sendSlackAlert("📈 BUSINESS CRITICAL ALERT", {
          businessHealth: aiInsights.overallBusinessHealth,
          valueScore: `${businessValueScore}/100`,
          keyMetrics: {
            efficiency: `${aiInsights.performanceMetrics.efficiencyScore}%`,
            reliability: `${aiInsights.performanceMetrics.reliabilityScore}%`,
            predictedVolume: `${aiInsights.predictions.next24hJobVolume} jobs`,
          },
          criticalInsights: aiInsights.keyInsights
            .filter((i) => i.priority === "immediate" && i.impact === "high")
            .slice(0, 3)
            .map((i) => i.insight),
          immediateActions: actionsRecommended,
          costOptimization: `${costOptimizationPotential}% potential savings`,
          timestamp: new Date().toISOString(),
        });
      });
    }

    // Step 9: Log Comprehensive Summary
    await context.run("log-comprehensive-summary", async () => {
      logger.info("📈 Business logic monitoring completed", {
        businessHealth: aiInsights.overallBusinessHealth,
        valueScore: businessValueScore,
        insights: aiInsights.keyInsights.length,
        recommendations: aiInsights.recommendations.length,
        costOptimization: costOptimizationPotential,
        actions: actionsRecommended,
        predictedVolume: aiInsights.predictions.next24hJobVolume,
        qualityTrend: aiInsights.predictions.qualityTrend,
      });
    });

    return {
      success: true,
      businessHealth: aiInsights.overallBusinessHealth,
      valueScore: businessValueScore,
      insights: aiInsights.keyInsights.length,
      recommendations: aiInsights.recommendations.length,
      costOptimizationPotential: costOptimizationPotential,
      actionsRecommended: actionsRecommended,
      executiveSummary: executiveSummary,
      predictions: aiInsights.predictions,
    };
  },
  {
    retries: 2,
    failureFunction: async ({ context, failStatus }) => {
      logger.error("🚨 Business logic monitoring workflow failed", {
        failStatus,
        payload: context.requestPayload,
        timestamp: new Date().toISOString(),
      });

      await sendSlackAlert("💥 BUSINESS MONITORING FAILURE", {
        message: "Business logic monitoring workflow has failed",
        failStatus,
        payload: context.requestPayload,
        timestamp: new Date().toISOString(),
        action: "Manual business review recommended",
        impact: "Business intelligence temporarily unavailable",
      });
    },
  }
);

// Manual trigger endpoint
export async function GET() {
  try {
    const workflowRunId = await triggerBusinessLogicMonitoring({
      monitoringType: "comprehensive",
      timeframe: "24h",
      includeForecasting: true,
      triggerSource: "manual_trigger",
    });

    return NextResponse.json({
      success: true,
      message: "Business logic monitoring triggered",
      workflowRunId,
    });
  } catch (error) {
    logger.error("Failed to trigger business logic monitoring", { error });

    return NextResponse.json(
      {
        success: false,
        error: "Failed to trigger business logic monitoring",
        details: (error as Error).message,
      },
      { status: 500 }
    );
  }
}
