import { NextResponse } from 'next/server';
import { logger } from '@/lib/logger';

export async function POST(request: Request) {
  try {
    const { type, ...options } = await request.json();

    const timestamp = new Date().toISOString();

    switch (type) {
      case 'critical':
        logger.critical(`🧪 Test Critical Notification (${timestamp})`, {
          source: 'test-ui',
          timestamp,
          testRun: true,
        });
        break;

      case 'alert':
        logger.alert(`🧪 Test Warning Notification (${timestamp})`, {
          source: 'test-ui',
          timestamp,
          testRun: true,
        });
        break;

      case 'info':
        logger.notify(`🧪 Test Info Notification (${timestamp})`, {
          source: 'test-ui',
          timestamp,
          testRun: true,
        });
        break;

      case 'pipeline':
        logger.pipeline({
          step: options.step || 'TEST',
          source: 'test-ui',
          success: options.success ?? true,
          jobCount: options.jobCount || 42,
          duration: options.duration || 3500,
          metadata: {
            testRun: true,
            timestamp,
            message: `🧪 Test Pipeline Notification (${timestamp})`,
          },
        });
        break;

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid notification type' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      message: `Test ${type} notification sent`,
      timestamp,
    });
  } catch (_error) {
    return NextResponse.json(
      { success: false, error: 'Failed to send test notification' },
      { status: 500 }
    );
  }
}
