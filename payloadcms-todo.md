# PayloadCMS Integration Plan for Bordfeed

## 📋 Overview

This document outlines the step-by-step implementation plan for integrating PayloadCMS alongside your existing Bordfeed system. The approach maintains your current job processing pipeline while gradually introducing PayloadCMS for content management and admin features.

## 🎯 Strategy: Parallel Implementation

- **Keep**: Current job processing, workflows, monitoring dashboards
- **Add**: PayloadCMS for user management, job board configs, content management
- **Route Separation**: `/dashboard` (current) + `/admin` (PayloadCMS)
- **Database**: Shared Supabase PostgreSQL database

## 📊 Current Database Analysis

### Existing Tables (47 total)
- **Core Job Tables**: `jobs`, `job_board_configs`, `job_board_postings`, `job_sources`
- **Monitoring**: `system_health_logs`, `data_quality_logs`, `business_logic_logs`, `job_monitor_logs`
- **Workflow**: `workflow_runs`, `notification_settings`
- **Airtable**: `airtable_secrets`
- **Auth**: Supabase auth schema (existing)
- **Directus**: Legacy tables (can be ignored/cleaned up later)

### Key Schema Insights
- **Jobs table**: 58 columns with complex JSONB fields (`ai_metadata`, `posted_to_boards`, `tags`)
- **UUID primary keys**: Jobs use `gen_random_uuid()`
- **Text primary keys**: Job board configs use text IDs
- **Rich metadata**: Extensive tracking fields for monitoring and processing

## 🚀 Implementation Phases

## Phase 1: Foundation Setup (Week 1)

### 1.1 Install PayloadCMS Dependencies
```bash
pnpm add payload @payloadcms/db-postgres @payloadcms/richtext-lexical
pnpm add @payloadcms/email-nodemailer  # For email functionality
```

### 1.2 Create PayloadCMS Configuration
**File**: `payload.config.ts`

Key considerations:
- Use existing Supabase PostgreSQL connection
- Configure to avoid conflicts with existing tables
- Set up proper schema naming to prevent collisions

### 1.3 Add PayloadCMS Routes
**Files to create**:
- `app/(payload)/admin/[[...segments]]/page.tsx`
- `app/(payload)/admin/[[...segments]]/not-found.tsx`
- `app/(payload)/api/[...slug]/route.ts`
- `app/(payload)/graphql/route.ts`

### 1.4 Environment Variables
**Add to `.env.local`**:
```bash
# PayloadCMS Configuration
PAYLOAD_SECRET=your_super_secure_secret_key_here
PAYLOAD_CONFIG_PATH=./payload.config.ts

# Database (reuse existing Supabase connection)
DATABASE_URL=your_existing_supabase_postgres_url

# Email (optional for Phase 1)
SMTP_HOST=your_smtp_host
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password
```

## Phase 2: Basic Collections (Week 2)

### 2.1 Users Collection (Authentication)
**Priority**: High
**Purpose**: Replace/enhance current user management

**Collection Config**:
- Enable authentication (`auth: true`)
- Map to existing Supabase auth or create new user system
- Add admin roles and permissions
- API key support for external integrations

### 2.2 Job Board Configurations Collection
**Priority**: High
**Purpose**: Manage job board configs through PayloadCMS admin

**Collection Config**:
- Map to existing `job_board_configs` table
- Rich editing for filters (JSONB field)
- Airtable configuration management
- Enable/disable boards easily

**Fields to implement**:
- `id` (text, required)
- `name` (text, required)
- `description` (textarea)
- `enabled` (checkbox)
- `airtable_base_id` (text)
- `airtable_table_name` (text)
- `daily_limit` (number)
- `filters` (JSON field with rich editor)
- `posting_times` (array field)

### 2.3 Jobs Collection (Read-Only Initially)
**Priority**: Medium
**Purpose**: View and manage jobs through PayloadCMS

**Collection Config**:
- Map to existing `jobs` table
- Read-only initially (no create/update)
- Rich filtering and search
- Relationship to job boards

**Key fields to expose**:
- Basic job info (title, company, description)
- Status and processing information
- Source tracking
- Airtable sync status

## Phase 3: Enhanced Features (Week 3-4)

### 3.1 Job Sources Collection
**Purpose**: Manage data sources (JobDataAPI, Workable, etc.)

**Map to**: `job_sources` table
**Features**:
- Source configuration
- Health monitoring integration
- Enable/disable sources

### 3.2 Notification Settings Global
**Purpose**: Centralized notification management

**Map to**: `notification_settings` table
**Features**:
- Slack configuration
- Email settings
- Notification preferences

### 3.3 System Settings Global
**Purpose**: Application-wide settings

**Features**:
- API rate limits
- Processing configurations
- Feature flags

## Phase 4: Advanced Integration (Month 2)

### 4.1 Monitoring Dashboard Integration
**Purpose**: Embed monitoring data in PayloadCMS

**Approach**:
- Custom dashboard components
- Read-only monitoring data
- Link to existing monitoring system

### 4.2 Job Processing Workflows
**Purpose**: Gradually migrate job workflows to PayloadCMS

**Considerations**:
- Start with simple workflows
- Keep complex AI processing in current system
- Use PayloadCMS jobs for basic automation

### 4.3 API Documentation Integration
**Purpose**: Manage API docs through PayloadCMS

**Features**:
- Rich text editing for documentation
- Version management
- Auto-generated API references

## 🔧 Technical Implementation Details

### Database Configuration Strategy

**Option 1: Shared Tables (Recommended)**
```typescript
// payload.config.ts
import { postgresAdapter } from '@payloadcms/db-postgres'

export default buildConfig({
  db: postgresAdapter({
    pool: {
      connectionString: process.env.DATABASE_URL, // Same as Supabase
    },
    // Prevent PayloadCMS from creating conflicting tables
    schemaName: 'payload', // Use separate schema
    // OR use table prefixes
    localesSuffix: '_payload_locales',
    relationshipsSuffix: '_payload_rels',
    versionsSuffix: '_payload_v',
  }),
})
```

**Option 2: Table Mapping**
```typescript
// Map PayloadCMS collections to existing tables
collections: [
  {
    slug: 'job-boards',
    // Map to existing job_board_configs table
    tableName: 'job_board_configs',
    fields: [
      {
        name: 'id',
        type: 'text',
        required: true,
      },
      // ... other fields
    ],
  },
]
```

### Authentication Strategy

**Recommended Approach**: Dual Authentication
1. **Keep Supabase Auth**: For existing application
2. **Add PayloadCMS Auth**: For admin users
3. **Future**: Migrate to PayloadCMS auth completely

### Route Configuration

```typescript
// next.config.js
module.exports = {
  // Ensure PayloadCMS routes don't conflict
  async rewrites() {
    return [
      {
        source: '/admin/:path*',
        destination: '/admin/:path*', // PayloadCMS admin
      },
      {
        source: '/dashboard/:path*',
        destination: '/dashboard/:path*', // Current dashboard
      },
    ]
  },
}
```

## 📝 Detailed Task Checklist

### Week 1: Foundation
- [ ] Install PayloadCMS dependencies
- [ ] Create basic `payload.config.ts`
- [ ] Set up PayloadCMS routes in Next.js
- [ ] Configure database connection (test with existing Supabase)
- [ ] Create basic Users collection with auth
- [ ] Test PayloadCMS admin panel access
- [ ] Verify no conflicts with existing routes

### Week 2: Core Collections
- [ ] Create Job Board Configurations collection
- [ ] Map to existing `job_board_configs` table
- [ ] Test CRUD operations on job boards
- [ ] Create read-only Jobs collection
- [ ] Implement basic job filtering and search
- [ ] Test data consistency between systems

### Week 3: Integration
- [ ] Create Job Sources collection
- [ ] Add Notification Settings global
- [ ] Implement rich text editing for descriptions
- [ ] Add file upload capabilities (if needed)
- [ ] Create custom admin components for complex fields

### Week 4: Polish & Testing
- [ ] Add proper access control and permissions
- [ ] Implement API key authentication
- [ ] Create custom dashboard components
- [ ] Add data validation and hooks
- [ ] Performance testing and optimization
- [ ] Documentation and training materials

## ⚠️ Risk Mitigation

### Database Conflicts
- Use separate schema or table prefixes
- Test thoroughly in development
- Have rollback plan ready

### Performance Impact
- Monitor database performance
- Use read replicas if needed
- Optimize PayloadCMS queries

### User Experience
- Maintain current dashboard during transition
- Train team on PayloadCMS interface
- Gradual feature migration

## 🎯 Success Metrics

### Phase 1 Success
- [ ] PayloadCMS admin accessible at `/admin`
- [ ] No conflicts with existing system
- [ ] Basic user authentication working

### Phase 2 Success
- [ ] Job board management through PayloadCMS
- [ ] Data consistency maintained
- [ ] Improved admin user experience

### Long-term Success
- [ ] Reduced maintenance overhead
- [ ] Faster feature development
- [ ] Better content management capabilities
- [ ] Maintained system performance

## 🔄 Migration Strategy

### Gradual Migration Approach
1. **Start**: PayloadCMS for new features only
2. **Migrate**: Non-critical admin features
3. **Evaluate**: Performance and user experience
4. **Decide**: Continue migration or maintain hybrid

### Rollback Plan
- Keep current system fully functional
- Feature flags for PayloadCMS features
- Database backup and restore procedures
- Quick disable mechanism for PayloadCMS

## 📚 Next Steps

1. **Review this plan** with your team/stakeholders
2. **Set up development environment** for testing
3. **Start with Phase 1** implementation
4. **Regular check-ins** to assess progress and adjust plan
5. **Document learnings** for future reference

---

**Note**: This plan prioritizes safety and gradual adoption. Each phase can be adjusted based on your experience and requirements. The goal is to gain PayloadCMS benefits while preserving your current system's strengths.
