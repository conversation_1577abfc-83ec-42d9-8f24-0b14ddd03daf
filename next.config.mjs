import { createMDX } from "fumadocs-mdx/next";
import { withPayload } from "@payloadcms/next/withPayload";

const withMDX = createMDX();

/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "www.google.com",
        port: "",
        pathname: "/s2/favicons**",
      },
    ],
  },
};

// Wrap with both PayloadCMS and MDX
export default withPayload(withMDX(nextConfig));
