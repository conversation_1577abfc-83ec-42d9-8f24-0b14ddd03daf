import { z } from "zod";
import { APPLY_METHODS } from "./apply-methods";
import { CAREER_LEVELS } from "./career-levels";
import { SCHEMA_LIMITS } from "./constants";
import { countries } from "./data/countries";
import { CURRENCY_CODES } from "./data/currencies";
import { LANGUAGE_CODES } from "./data/languages";
import { JOB_STATUSES, VISA_SPONSORSHIP_OPTIONS } from "./job-status";
import { JOB_TYPES } from "./job-types";
import { SALARY_UNITS } from "./salary-units";
import { REMOTE_REGIONS, WORKPLACE_TYPES } from "./workplace";

// Accept ISO date or datetime strings
const IsoDateOrDateTimeSchema = z.string();

// Simplified schema for AI extraction - optimized for AI SDK v5 strict validation
export const JobExtractionSchema = z.object({
  // Core fields (required)
  title: z.string(),
  description: z.string(),
  status: z.enum(JOB_STATUSES).default("active"),

  // Core fields (nullable for AI extraction flexibility)
  company: z.string().nullable(),
  type: z.enum(JOB_TYPES).nullable(),
  apply_url: z.string().nullable(),
  apply_method: z.enum(APPLY_METHODS).nullable(),
  posted_date: IsoDateOrDateTimeSchema.nullable(),

  // Salary information
  salary_min: z.number().int().positive().nullable(),
  salary_max: z.number().int().positive().nullable(),
  salary_currency: z.enum(CURRENCY_CODES).nullable(),
  salary_unit: z.enum(SALARY_UNITS).nullable(),

  // Location & Remote work
  workplace_type: z.enum(WORKPLACE_TYPES).nullable(),
  remote_region: z.enum(REMOTE_REGIONS).nullable(),
  timezone_requirements: z.string().nullable(),
  workplace_city: z.string().nullable(),
  workplace_country: z.enum(countries).nullable(),

  // Additional details
  benefits: z.string().max(SCHEMA_LIMITS.benefits).nullable(),
  application_requirements: z
    .string()
    .max(SCHEMA_LIMITS.application_requirements)
    .nullable(),
  valid_through: IsoDateOrDateTimeSchema.nullable(),
  job_identifier: z.string().nullable(),
  job_source_name: z.string().nullable(),
  department: z.string().nullable(),
  travel_required: z.boolean().nullable(),

  // Career & Skills
  career_level: z.array(z.enum(CAREER_LEVELS)).nullable(),
  visa_sponsorship: z.enum(VISA_SPONSORSHIP_OPTIONS).nullable(),
  languages: z.array(z.enum(LANGUAGE_CODES)).nullable(),
  skills: z.string().max(SCHEMA_LIMITS.skills).nullable(),
  qualifications: z.string().max(SCHEMA_LIMITS.qualifications).nullable(),
  education_requirements: z
    .string()
    .max(SCHEMA_LIMITS.education_requirements)
    .nullable(),
  experience_requirements: z
    .string()
    .max(SCHEMA_LIMITS.experience_requirements)
    .nullable(),
  responsibilities: z.string().max(SCHEMA_LIMITS.responsibilities).nullable(),

  // SEO & Classification
  featured: z.boolean().nullable(),
  industry: z.string().nullable(),
  occupational_category: z.string().nullable(),
});

// Full schema including metadata
export const JobSchema = z.object({
  sourcedAt: z.string().datetime(),
  sourceUrl: z.string().url(),
  ...JobExtractionSchema.shape,
});

export type Job = z.infer<typeof JobSchema>;
export type JobExtraction = z.infer<typeof JobExtractionSchema>;
