import { useCallback, useState } from "react";
import { logger } from "@/lib/utils";

interface ProcessingState {
  [jobId: string]: {
    isProcessing: boolean;
    error?: string;
  };
}

export function useJobProcessing() {
  const [processingState, setProcessingState] = useState<ProcessingState>({});

  const startProcessing = useCallback((jobId: string) => {
    setProcessingState((prev) => ({
      ...prev,
      [jobId]: { isProcessing: true },
    }));
  }, []);

  const clearProcessing = useCallback((jobId: string) => {
    setProcessingState((prev) => {
      const newState = { ...prev };
      delete newState[jobId];
      return newState;
    });
  }, []);

  const setFailure = useCallback((jobId: string, errorMessage: string) => {
    try {
      (
        window as unknown as { lastProcessJobError?: string }
      ).lastProcessJobError = errorMessage;
    } catch {
      // window may be unavailable
    }

    setProcessingState((prev) => ({
      ...prev,
      [jobId]: { isProcessing: false, error: errorMessage },
    }));
  }, []);

  const computeDetailsText = useCallback((raw: unknown): string | undefined => {
    if (typeof raw === "string") {
      return raw;
    }
    if (raw === undefined || raw === null) {
      return;
    }
    try {
      return JSON.stringify(raw);
    } catch {
      return String(raw);
    }
  }, []);

  const composeErrorMessageFromResponse = useCallback(
    async (response: Response): Promise<string> => {
      const errorData = await response.json().catch(() => ({}) as unknown);
      const anyData = errorData as Record<string, unknown> | undefined;

      // Helper to summarize Zod/validation issues from the API payload
      const summarizeValidationIssues = (
        data: Record<string, unknown> | undefined
      ): string | undefined => {
        if (!data) {
          return;
        }
        // Look for validation errors in common locations
        const topLevelIssues = data.validationErrors as unknown;
        // biome-ignore lint/suspicious/noExplicitAny: Error payload is dynamic
        const nestedIssues = (data.details as any)?.validationErrors as unknown;
        const issues = (
          Array.isArray(topLevelIssues)
            ? topLevelIssues
            : Array.isArray(nestedIssues)
              ? nestedIssues
              : undefined
        ) as Array<Record<string, unknown>> | undefined;

        if (!issues || issues.length === 0) {
          return;
        }

        const toText = (issue: Record<string, unknown>) => {
          const path = Array.isArray(issue.path)
            ? (issue.path as Array<string | number>).join(".")
            : (issue.path as string | undefined) || "<unknown>";
          const message = (issue.message as string | undefined) || "Invalid";
          const expected = issue.expected as string | undefined;
          const received = issue.received as string | undefined;
          const details = [message]
            .concat(expected ? `expected ${expected}` : [])
            .concat(received ? `received ${received}` : [])
            .join("; ");
          return `${path}: ${details}`;
        };

        const preview = issues
          .slice(0, 3)
          .map((i) => toText(i))
          .join(" | ");
        const extra = issues.length > 3 ? ` (+${issues.length - 3} more)` : "";
        return `Issues: ${preview}${extra}`;
      };

      // Handle enhanced error responses from the new API
      if (anyData?.type && anyData?.suggestion) {
        const errorType = anyData.type as string;
        const suggestion = anyData.suggestion as string;
        const isTemporary = anyData.temporary === true;
        const issuesSummary = summarizeValidationIssues(anyData);

        // Create user-friendly error messages based on error type
        if (
          errorType === "schema_validation" ||
          errorType === "ai_service_failure"
        ) {
          return [
            suggestion,
            isTemporary ? "(Temporary issue)" : undefined,
            issuesSummary,
          ]
            .filter(Boolean)
            .join(" — ");
        }

        if (errorType === "temporary") {
          return `${suggestion}`;
        }
      }

      // Fallback to original error handling
      const rawDetails = anyData?.details ?? (anyData?.detail as unknown);
      const detailsText = computeDetailsText(rawDetails);
      const statusText = `HTTP ${response.status}`;
      const prefix =
        (anyData?.error as string | undefined) || "Failed to process job";
      return [prefix, statusText, detailsText].filter(Boolean).join(" — ");
    },
    [computeDetailsText]
  );

  const processJob = useCallback(
    async (jobId: string, onSuccess?: () => void) => {
      startProcessing(jobId);

      try {
        const response = await fetch("/api/jobs", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            action: "processNow",
            id: jobId,
          }),
        });

        if (!response.ok) {
          const message = await composeErrorMessageFromResponse(response);
          throw new Error(message);
        }

        const result = await response.json();

        clearProcessing(jobId);

        if (onSuccess) {
          onSuccess();
        }

        logger.info(`Job ${jobId} processed successfully`, result);

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Processing failed";

        setFailure(jobId, errorMessage);

        logger.error(`Failed to process job ${jobId}`, { error: errorMessage });
        throw error;
      }
    },
    [
      startProcessing,
      composeErrorMessageFromResponse,
      clearProcessing,
      setFailure,
    ]
  );

  const clearError = useCallback((jobId: string) => {
    setProcessingState((prev) => {
      const newState = { ...prev };
      delete newState[jobId];
      return newState;
    });
  }, []);

  const isProcessing = useCallback(
    (jobId: string) => {
      return processingState[jobId]?.isProcessing;
    },
    [processingState]
  );

  const getError = useCallback(
    (jobId: string) => {
      return processingState[jobId]?.error;
    },
    [processingState]
  );

  return {
    processJob,
    isProcessing,
    getError,
    clearError,
  };
}
