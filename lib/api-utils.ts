import { NextResponse } from "next/server";
import type { ZodError } from "zod";
import { DEBUG_MODE } from "./constants";
import { logger } from "./utils";

/**
 * Standard error response for validation failures
 */
export function validationErrorResponse(error: ZodError) {
  return NextResponse.json(
    {
      error: "Invalid request body",
      details: error.flatten().fieldErrors,
    },
    { status: 400 }
  );
}

// Type for error cause object
interface ErrorCause {
  issues?: unknown[];
  text?: string;
  value?: unknown;
  [key: string]: unknown;
}

/**
 * Process AI response text from error cause
 */
function processAiResponseText(text: string): {
  aiResponse?: unknown;
  aiResponseRaw?: string;
} {
  try {
    const parsed = JSON.parse(text);
    if (DEBUG_MODE) {
      logger.log("Parsed AI response:", parsed);
    }
    return { aiResponse: parsed };
  } catch {
    if (DEBUG_MODE) {
      logger.log("Raw AI response (failed to parse):", text);
    }
    return { aiResponseRaw: text };
  }
}

/**
 * Extract validation details from error cause
 */
function extractCauseDetails(cause: ErrorCause): Record<string, unknown> {
  const details: Record<string, unknown> = {};

  if ("issues" in cause) {
    details.validationErrors = cause.issues;
  }

  if ("text" in cause && cause.text) {
    Object.assign(details, processAiResponseText(cause.text));
  }

  if ("value" in cause) {
    details.parsedValue = cause.value;
    if (DEBUG_MODE) {
      logger.log("Parsed value from cause:", cause.value);
    }
  }

  return details;
}

/**
 * Extract and format schema mismatch error details
 */
export function extractSchemaErrorDetails(
  error: Error,
  contextName = "Schema"
) {
  const errorDetails: Record<string, unknown> = {
    error: "Schema validation failed",
    message: `The AI response did not match the expected ${contextName.toLowerCase()} schema`,
    detail: error.message,
  };

  // Extract validation issues if available
  if ("cause" in error && error.cause && typeof error.cause === "object") {
    const cause = error.cause as ErrorCause;
    Object.assign(errorDetails, extractCauseDetails(cause));
  }

  if (DEBUG_MODE) {
    logger.log(`${contextName} validation error details:`, errorDetails);
  }

  return errorDetails;
}

/**
 * Determine if an error is likely temporary/retryable
 */
function isTemporaryError(error: Error): boolean {
  const message = error.message.toLowerCase();

  // Network/timeout errors
  if (
    message.includes("timeout") ||
    message.includes("network") ||
    message.includes("econnreset") ||
    message.includes("etimedout") ||
    message.includes("socket hang up")
  ) {
    return true;
  }

  // Rate limiting
  if (message.includes("rate limit") || message.includes("429")) {
    return true;
  }

  // AI service temporary issues
  if (
    message.includes("response did not match schema") ||
    message.includes("ai extraction failed after")
  ) {
    return true;
  }

  return false;
}

/**
 * Standard error handler for API routes
 */
export function handleApiError(error: unknown, contextName = "Operation") {
  if (!(error instanceof Error)) {
    return NextResponse.json(
      {
        error: "Internal server error",
        detail: "Unknown error occurred",
        type: "unknown",
      },
      { status: 500 }
    );
  }

  // Handle schema mismatch errors with enhanced messaging
  if (error.message.includes("response did not match schema")) {
    const errorDetails = extractSchemaErrorDetails(error, contextName);

    // Check if this was after retry attempts
    const isAfterRetry = error.message.includes("failed after");

    return NextResponse.json(
      {
        ...errorDetails,
        type: "schema_validation",
        temporary: true,
        suggestion: isAfterRetry
          ? "The AI service is experiencing issues. Please try again in a few minutes."
          : "Schema validation failed. This may be a temporary issue.",
      },
      { status: 422 }
    );
  }

  // Handle AI extraction failures with retry information
  if (error.message.includes("AI extraction failed after")) {
    return NextResponse.json(
      {
        error: "AI processing temporarily unavailable",
        message:
          "The AI service failed to process this job after multiple attempts.",
        detail: error.message,
        type: "ai_service_failure",
        temporary: true,
        suggestion:
          "Please try again in a few minutes. If the problem persists, contact support.",
      },
      { status: 503 }
    ); // Service Unavailable
  }

  // Determine if error is temporary
  const temporary = isTemporaryError(error);

  return NextResponse.json(
    {
      error: temporary ? "Temporary service error" : "Internal server error",
      detail: error.message,
      type: temporary ? "temporary" : "permanent",
      temporary,
      suggestion: temporary
        ? "This appears to be a temporary issue. Please try again in a moment."
        : "An unexpected error occurred. Please contact support if this persists.",
    },
    { status: temporary ? 503 : 500 }
  );
}

/**
 * Creates a standardized health check response for webhook endpoints
 */
export function createHealthCheckResponse(config: {
  service: string;
  status?: string;
  architecture?: string;
  features?: string[];
  environment?: Record<string, unknown>;
}) {
  return NextResponse.json({
    service: config.service,
    status: config.status || "healthy",
    architecture: config.architecture,
    features: config.features || [],
    environment: config.environment || {},
    timestamp: new Date().toISOString(),
  });
}

/**
 * Replace template placeholders in a string
 */
export function replaceTemplateVariables(
  template: string,
  variables: Record<string, string>
): string {
  let result = template;
  for (const [key, value] of Object.entries(variables)) {
    result = result.replaceAll(`{${key}}`, value);
  }
  return result;
}
