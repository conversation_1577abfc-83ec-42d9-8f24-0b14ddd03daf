/**
 * Status reconciliation utilities to fix inconsistent job states
 */

import { supabase } from "./supabase";
import { logger } from "./logger";

export interface ReconciliationResult {
  fixed: number;
  errors: string[];
  details: Array<{
    id: string;
    action: string;
    success: boolean;
    error?: string;
  }>;
}

/**
 * Find jobs that have AI metadata but are still marked as pending
 */
export async function findJobsNeedingReconciliation() {
  const { data, error } = await supabase
    .from("jobs")
    .select("id, title, processing_status, ai_metadata, updated_at")
    .eq("processing_status", "pending")
    .not("ai_metadata", "is", null);

  if (error) {
    throw new Error(
      `Failed to query jobs for reconciliation: ${error.message}`
    );
  }

  return data || [];
}

/**
 * Fix a single job's status from pending to completed
 */
async function fixJobStatus(
  jobId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from("jobs")
      .update({
        processing_status: "completed",
        updated_at: new Date().toISOString(),
      })
      .eq("id", jobId);

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Reconcile job statuses - fix jobs that have AI metadata but are marked as pending
 */
export async function reconcileJobStatuses(): Promise<ReconciliationResult> {
  const result: ReconciliationResult = {
    fixed: 0,
    errors: [],
    details: [],
  };

  try {
    // Find jobs that need reconciliation
    const jobsToFix = await findJobsNeedingReconciliation();

    logger.info(
      `Found ${jobsToFix.length} jobs that need status reconciliation`
    );

    // Fix each job
    for (const job of jobsToFix) {
      const fixResult = await fixJobStatus(job.id);

      result.details.push({
        id: job.id,
        action: "update_status_to_completed",
        success: fixResult.success,
        error: fixResult.error,
      });

      if (fixResult.success) {
        result.fixed++;
        logger.info(`Fixed job ${job.id}: ${job.title}`);
      } else {
        result.errors.push(`Failed to fix job ${job.id}: ${fixResult.error}`);
        logger.error(`Failed to fix job ${job.id}:`, fixResult.error);
      }
    }

    logger.info(
      `Status reconciliation completed: ${result.fixed} jobs fixed, ${result.errors.length} errors`
    );
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    result.errors.push(`Reconciliation failed: ${errorMessage}`);
    logger.error("Status reconciliation failed:", errorMessage);
  }

  return result;
}

/**
 * Get statistics about job status inconsistencies
 */
export async function getStatusInconsistencyStats() {
  try {
    // Jobs with AI metadata but pending status
    const { count: pendingWithAI } = await supabase
      .from("jobs")
      .select("*", { count: "exact", head: true })
      .eq("processing_status", "pending")
      .not("ai_metadata", "is", null);

    // Jobs marked completed but no processed_at timestamp
    const { count: completedWithoutTimestamp } = await supabase
      .from("jobs")
      .select("*", { count: "exact", head: true })
      .eq("processing_status", "completed")
      .is("processed_at", null);

    // Total jobs by status
    const { data: statusCounts } = await supabase
      .from("jobs")
      .select("processing_status")
      .then(({ data }) => {
        const counts = data?.reduce(
          (acc, job) => {
            acc[job.processing_status] = (acc[job.processing_status] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>
        );
        return { data: counts };
      });

    return {
      pendingWithAI: pendingWithAI || 0,
      completedWithoutTimestamp: completedWithoutTimestamp || 0,
      statusCounts: statusCounts || {},
      needsReconciliation: (pendingWithAI || 0) > 0,
    };
  } catch (error) {
    logger.error("Failed to get status inconsistency stats:", error);
    throw error;
  }
}

/**
 * Trigger status reconciliation API endpoint (for use in workflows)
 */
export async function triggerStatusReconciliation(): Promise<ReconciliationResult> {
  try {
    logger.info("🔄 Triggering status reconciliation...");

    // Call the reconciliation function directly instead of HTTP endpoint
    // This avoids circular dependencies and is more efficient
    const result = await reconcileJobStatuses();

    logger.info("✅ Status reconciliation completed", {
      fixed: result.fixed,
      errors: result.errors.length,
    });

    return result;
  } catch (error) {
    logger.error("❌ Failed to trigger status reconciliation", { error });
    throw error;
  }
}
