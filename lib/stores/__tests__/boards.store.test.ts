/**
 * Basic smoke tests for Boards store
 *
 * These tests verify that critical store actions work without errors
 * and maintain proper state consistency.
 */

// @ts-expect-error - Testing library types not available in production build
import { act, renderHook } from '@testing-library/react';
// @ts-expect-error - Vitest types not available in production build
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useBoardsStore } from '../boards.store';

// Mock fetch globally
global.fetch = vi.fn() as unknown as typeof fetch;

describe('BoardsStore Smoke Tests', () => {
  beforeEach(() => {
    // Reset store state before each test
    useBoardsStore.setState({
      boards: [],
      availablePats: [],
      loading: false,
      error: null,
      lastFetch: null,
    });

    // Reset fetch mock
    vi.clearAllMocks();
  });

  it('should initialize with default state', () => {
    const { result } = renderHook(() => useBoardsStore());

    expect(result.current.boards).toEqual([]);
    expect(result.current.patStatus).toEqual({});
    expect(result.current.selectedBoard).toBe(null);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it('should handle fetch action without crashing', async () => {
    const mockBoardsResponse = {
      ok: true,
      json: async () => ({
        success: true,
        boards: [{ id: '1', name: 'Test Board', is_active: true }],
      }),
    };

    const mockPatResponse = {
      ok: true,
      json: async () => ({
        success: true,
        availablePats: { '1': ['token123'] },
      }),
    };

    (global.fetch as ReturnType<typeof vi.fn>)
      .mockResolvedValueOnce(mockBoardsResponse)
      .mockResolvedValueOnce(mockPatResponse);

    const { result } = renderHook(() => useBoardsStore());

    // Test fetch action
    await act(async () => {
      await result.current.actions.fetch();
    });

    // Verify state was updated
    expect(result.current.boards).toHaveLength(1);
    expect(result.current.boards[0].name).toBe('Test Board');
    expect(result.current.patStatus['1']).toBe('configured');
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it('should handle fetch errors gracefully', async () => {
    (global.fetch as ReturnType<typeof vi.fn>).mockRejectedValueOnce(
      new Error('Network error')
    );

    const { result } = renderHook(() => useBoardsStore());

    await act(async () => {
      await result.current.actions.fetch();
    });

    // Verify error state
    expect(result.current.error).toBe('Network error');
    expect(result.current.loading).toBe(false);
    expect(result.current.boards).toEqual([]);
  });

  it('should handle createBoard action without crashing', async () => {
    const mockResponse = {
      ok: true,
      json: async () => ({
        success: true,
        board: { id: '2', name: 'New Board', is_active: true },
      }),
    };

    (global.fetch as ReturnType<typeof vi.fn>).mockResolvedValueOnce(
      mockResponse
    );

    const { result } = renderHook(() => useBoardsStore());

    // Test createBoard action
    await act(async () => {
      await result.current.actions.createBoard({
        name: 'New Board',
        is_active: true,
      });
    });

    // Verify board was added
    expect(result.current.boards).toHaveLength(1);
    expect(result.current.boards[0].name).toBe('New Board');
  });

  it('should handle updateBoard action without crashing', async () => {
    const mockResponse = {
      ok: true,
      json: async () => ({ success: true }),
    };

    (global.fetch as ReturnType<typeof vi.fn>).mockResolvedValueOnce(
      mockResponse
    );

    const { result } = renderHook(() => useBoardsStore());

    // Set initial state with a board
    act(() => {
      useBoardsStore.setState({
        boards: [
          {
            id: '1',
            name: 'Original Board',
            enabled: true,
            airtable: {
              baseId: 'test-base',
              tableName: 'test-table',
            },
          },
        ],
      });
    });

    // Test updateBoard action
    await act(async () => {
      await result.current.actions.updateBoard('1', {
        name: 'Updated Board',
      });
    });

    // Verify board was updated
    expect(result.current.boards[0].name).toBe('Updated Board');
    expect(result.current.boards[0].enabled).toBe(true); // Should preserve other fields
  });

  it('should handle sendToAirtable with optimistic updates', async () => {
    const mockResponse = { ok: true };
    (global.fetch as ReturnType<typeof vi.fn>).mockResolvedValueOnce(
      mockResponse
    );

    const { result } = renderHook(() => useBoardsStore());

    // Set initial state with a board
    act(() => {
      useBoardsStore.setState({
        boards: [
          {
            id: '1',
            name: 'Test Board',
            lastSync: null,
            airtable: {
              baseId: 'test-base',
              tableName: 'test-table',
            },
          },
        ],
      });
    });

    // Test sendToAirtable action
    await act(async () => {
      await result.current.actions.sendToAirtable('1', ['job1', 'job2']);
    });

    // Verify optimistic update was applied
    expect(result.current.boards[0].lastSync).toBeTruthy();
  });
});
