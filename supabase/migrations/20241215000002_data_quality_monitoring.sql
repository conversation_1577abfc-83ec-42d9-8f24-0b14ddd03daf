-- Create data quality monitoring table
CREATE TABLE IF NOT EXISTS data_quality_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  dataset_type TEXT NOT NULL DEFAULT 'job_processing',
  total_items INTEGER NOT NULL DEFAULT 0,
  
  -- Overall quality metrics
  overall_score INTEGER NOT NULL DEFAULT 0 CHECK (overall_score >= 0 AND overall_score <= 100),
  overall_quality TEXT NOT NULL CHECK (overall_quality IN ('EXCELLENT', 'GOOD', 'FAIR', 'POOR', 'CRITICAL')),
  confidence DECIMAL(3,2) NOT NULL DEFAULT 0.0 CHECK (confidence >= 0.0 AND confidence <= 1.0),
  
  -- Detailed quality metrics
  data_completeness DECIMAL(3,2) NOT NULL DEFAULT 0.0 CHECK (data_completeness >= 0.0 AND data_completeness <= 1.0),
  schema_compliance DECIMAL(3,2) NOT NULL DEFAULT 0.0 CHECK (schema_compliance >= 0.0 AND schema_compliance <= 1.0),
  data_consistency DECIMAL(3,2) NOT NULL DEFAULT 0.0 CHECK (data_consistency >= 0.0 AND data_consistency <= 1.0),
  ai_extraction_success DECIMAL(3,2) NOT NULL DEFAULT 0.0 CHECK (ai_extraction_success >= 0.0 AND ai_extraction_success <= 1.0),
  
  -- Issue tracking
  issues_count INTEGER NOT NULL DEFAULT 0,
  critical_issues_count INTEGER NOT NULL DEFAULT 0,
  
  -- Action tracking
  actions_taken JSONB DEFAULT '[]'::jsonb,
  remediation_triggered BOOLEAN NOT NULL DEFAULT false,
  
  -- Raw analysis data for debugging
  raw_data JSONB,
  
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_data_quality_logs_timestamp ON data_quality_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_data_quality_logs_overall_quality ON data_quality_logs(overall_quality);
CREATE INDEX IF NOT EXISTS idx_data_quality_logs_dataset_type ON data_quality_logs(dataset_type);
CREATE INDEX IF NOT EXISTS idx_data_quality_logs_score ON data_quality_logs(overall_score DESC);
CREATE INDEX IF NOT EXISTS idx_data_quality_logs_critical_issues ON data_quality_logs(critical_issues_count DESC);

-- Create a composite index for time-series queries with quality filtering
CREATE INDEX IF NOT EXISTS idx_data_quality_logs_time_quality ON data_quality_logs(timestamp DESC, overall_quality);

-- Add RLS (Row Level Security)
ALTER TABLE data_quality_logs ENABLE ROW LEVEL SECURITY;

-- Create policy for service role access (for monitoring workflows)
CREATE POLICY "Service role can manage data quality logs" ON data_quality_logs
  FOR ALL USING (auth.role() = 'service_role');

-- Create policy for authenticated users to read data quality logs
CREATE POLICY "Authenticated users can read data quality logs" ON data_quality_logs
  FOR SELECT USING (auth.role() = 'authenticated');

-- Create a function to clean up old data quality logs (keep last 60 days)
CREATE OR REPLACE FUNCTION cleanup_old_data_quality_logs()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM data_quality_logs 
  WHERE timestamp < NOW() - INTERVAL '60 days';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a view for data quality trends
CREATE OR REPLACE VIEW data_quality_trends AS
SELECT 
  DATE_TRUNC('hour', timestamp) as hour,
  dataset_type,
  overall_quality,
  COUNT(*) as check_count,
  AVG(overall_score) as avg_score,
  AVG(data_completeness) as avg_completeness,
  AVG(schema_compliance) as avg_schema_compliance,
  AVG(data_consistency) as avg_consistency,
  AVG(ai_extraction_success) as avg_ai_success,
  SUM(critical_issues_count) as total_critical_issues,
  SUM(CASE WHEN remediation_triggered THEN 1 ELSE 0 END) as remediations_triggered,
  MAX(timestamp) as latest_check
FROM data_quality_logs 
WHERE timestamp >= NOW() - INTERVAL '7 days'
GROUP BY DATE_TRUNC('hour', timestamp), dataset_type, overall_quality
ORDER BY hour DESC, dataset_type, overall_quality;

-- Create a view for current data quality status
CREATE OR REPLACE VIEW current_data_quality_status AS
WITH latest_checks AS (
  SELECT DISTINCT ON (dataset_type) 
    *
  FROM data_quality_logs 
  ORDER BY dataset_type, timestamp DESC
)
SELECT 
  dataset_type,
  overall_quality,
  overall_score,
  confidence,
  data_completeness,
  schema_compliance,
  data_consistency,
  ai_extraction_success,
  issues_count,
  critical_issues_count,
  remediation_triggered,
  timestamp as last_check,
  EXTRACT(EPOCH FROM (NOW() - timestamp))/3600 as hours_since_check
FROM latest_checks;

-- Create a function to get data quality summary for dashboard
CREATE OR REPLACE FUNCTION get_data_quality_summary(hours_back INTEGER DEFAULT 24)
RETURNS TABLE (
  total_checks INTEGER,
  avg_score DECIMAL,
  excellent_count INTEGER,
  good_count INTEGER,
  fair_count INTEGER,
  poor_count INTEGER,
  critical_count INTEGER,
  total_issues INTEGER,
  critical_issues INTEGER,
  remediations_triggered INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::INTEGER as total_checks,
    ROUND(AVG(overall_score), 1) as avg_score,
    COUNT(CASE WHEN overall_quality = 'EXCELLENT' THEN 1 END)::INTEGER as excellent_count,
    COUNT(CASE WHEN overall_quality = 'GOOD' THEN 1 END)::INTEGER as good_count,
    COUNT(CASE WHEN overall_quality = 'FAIR' THEN 1 END)::INTEGER as fair_count,
    COUNT(CASE WHEN overall_quality = 'POOR' THEN 1 END)::INTEGER as poor_count,
    COUNT(CASE WHEN overall_quality = 'CRITICAL' THEN 1 END)::INTEGER as critical_count,
    SUM(issues_count)::INTEGER as total_issues,
    SUM(critical_issues_count)::INTEGER as critical_issues,
    SUM(CASE WHEN remediation_triggered THEN 1 ELSE 0 END)::INTEGER as remediations_triggered
  FROM data_quality_logs
  WHERE timestamp >= NOW() - (hours_back || ' hours')::INTERVAL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
