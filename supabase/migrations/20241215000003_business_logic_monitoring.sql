-- Create business logic monitoring table
CREATE TABLE IF NOT EXISTS business_logic_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  monitoring_type TEXT NOT NULL DEFAULT 'comprehensive',
  timeframe TEXT NOT NULL DEFAULT '24h',
  
  -- Overall business health metrics
  business_health TEXT NOT NULL CHECK (business_health IN ('EXCELLENT', 'GOOD', 'FAIR', 'CONCERNING', 'CRITICAL')),
  confidence DECIMAL(3,2) NOT NULL DEFAULT 0.0 CHECK (confidence >= 0.0 AND confidence <= 1.0),
  business_value_score INTEGER NOT NULL DEFAULT 0 CHECK (business_value_score >= 0 AND business_value_score <= 100),
  
  -- Performance metrics
  efficiency_score INTEGER NOT NULL DEFAULT 0 CHECK (efficiency_score >= 0 AND efficiency_score <= 100),
  scalability_index INTEGER NOT NULL DEFAULT 0 CHECK (scalability_index >= 0 AND scalability_index <= 100),
  reliability_score INTEGER NOT NULL DEFAULT 0 CHECK (reliability_score >= 0 AND reliability_score <= 100),
  innovation_index INTEGER NOT NULL DEFAULT 0 CHECK (innovation_index >= 0 AND innovation_index <= 100),
  
  -- Predictions and forecasting
  predicted_job_volume INTEGER NOT NULL DEFAULT 0,
  cost_optimization_potential INTEGER NOT NULL DEFAULT 0 CHECK (cost_optimization_potential >= 0 AND cost_optimization_potential <= 100),
  
  -- Insights and recommendations tracking
  insights_count INTEGER NOT NULL DEFAULT 0,
  recommendations_count INTEGER NOT NULL DEFAULT 0,
  alert_level TEXT NOT NULL DEFAULT 'info' CHECK (alert_level IN ('none', 'info', 'warning', 'critical')),
  
  -- Action tracking
  actions_recommended JSONB DEFAULT '[]'::jsonb,
  executive_summary TEXT,
  
  -- Raw analysis data for debugging and detailed review
  raw_data JSONB,
  
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_business_logic_logs_timestamp ON business_logic_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_business_logic_logs_business_health ON business_logic_logs(business_health);
CREATE INDEX IF NOT EXISTS idx_business_logic_logs_monitoring_type ON business_logic_logs(monitoring_type);
CREATE INDEX IF NOT EXISTS idx_business_logic_logs_value_score ON business_logic_logs(business_value_score DESC);
CREATE INDEX IF NOT EXISTS idx_business_logic_logs_alert_level ON business_logic_logs(alert_level);

-- Create a composite index for time-series queries with health filtering
CREATE INDEX IF NOT EXISTS idx_business_logic_logs_time_health ON business_logic_logs(timestamp DESC, business_health);

-- Create a composite index for performance analysis
CREATE INDEX IF NOT EXISTS idx_business_logic_logs_performance ON business_logic_logs(efficiency_score DESC, scalability_index DESC, reliability_score DESC);

-- Add RLS (Row Level Security)
ALTER TABLE business_logic_logs ENABLE ROW LEVEL SECURITY;

-- Create policy for service role access (for monitoring workflows)
CREATE POLICY "Service role can manage business logic logs" ON business_logic_logs
  FOR ALL USING (auth.role() = 'service_role');

-- Create policy for authenticated users to read business logic logs
CREATE POLICY "Authenticated users can read business logic logs" ON business_logic_logs
  FOR SELECT USING (auth.role() = 'authenticated');

-- Create a function to clean up old business logic logs (keep last 90 days for business intelligence)
CREATE OR REPLACE FUNCTION cleanup_old_business_logic_logs()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM business_logic_logs 
  WHERE timestamp < NOW() - INTERVAL '90 days';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a view for business performance trends
CREATE OR REPLACE VIEW business_performance_trends AS
SELECT 
  DATE_TRUNC('hour', timestamp) as hour,
  monitoring_type,
  business_health,
  COUNT(*) as check_count,
  AVG(business_value_score) as avg_value_score,
  AVG(efficiency_score) as avg_efficiency,
  AVG(scalability_index) as avg_scalability,
  AVG(reliability_score) as avg_reliability,
  AVG(innovation_index) as avg_innovation,
  AVG(predicted_job_volume) as avg_predicted_volume,
  AVG(cost_optimization_potential) as avg_cost_optimization,
  SUM(insights_count) as total_insights,
  SUM(recommendations_count) as total_recommendations,
  MAX(timestamp) as latest_check
FROM business_logic_logs 
WHERE timestamp >= NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('hour', timestamp), monitoring_type, business_health
ORDER BY hour DESC, monitoring_type, business_health;

-- Create a view for current business status
CREATE OR REPLACE VIEW current_business_status AS
WITH latest_business_check AS (
  SELECT DISTINCT ON (monitoring_type) 
    *
  FROM business_logic_logs 
  ORDER BY monitoring_type, timestamp DESC
)
SELECT 
  monitoring_type,
  business_health,
  business_value_score,
  confidence,
  efficiency_score,
  scalability_index,
  reliability_score,
  innovation_index,
  predicted_job_volume,
  cost_optimization_potential,
  insights_count,
  recommendations_count,
  alert_level,
  executive_summary,
  timestamp as last_check,
  EXTRACT(EPOCH FROM (NOW() - timestamp))/3600 as hours_since_check
FROM latest_business_check;

-- Create a function to get business KPI summary
CREATE OR REPLACE FUNCTION get_business_kpi_summary(hours_back INTEGER DEFAULT 168) -- Default 7 days
RETURNS TABLE (
  total_checks INTEGER,
  avg_value_score DECIMAL,
  excellent_count INTEGER,
  good_count INTEGER,
  fair_count INTEGER,
  concerning_count INTEGER,
  critical_count INTEGER,
  avg_efficiency DECIMAL,
  avg_scalability DECIMAL,
  avg_reliability DECIMAL,
  avg_innovation DECIMAL,
  total_insights INTEGER,
  total_recommendations INTEGER,
  avg_cost_optimization DECIMAL,
  avg_predicted_volume DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::INTEGER as total_checks,
    ROUND(AVG(business_value_score), 1) as avg_value_score,
    COUNT(CASE WHEN business_health = 'EXCELLENT' THEN 1 END)::INTEGER as excellent_count,
    COUNT(CASE WHEN business_health = 'GOOD' THEN 1 END)::INTEGER as good_count,
    COUNT(CASE WHEN business_health = 'FAIR' THEN 1 END)::INTEGER as fair_count,
    COUNT(CASE WHEN business_health = 'CONCERNING' THEN 1 END)::INTEGER as concerning_count,
    COUNT(CASE WHEN business_health = 'CRITICAL' THEN 1 END)::INTEGER as critical_count,
    ROUND(AVG(efficiency_score), 1) as avg_efficiency,
    ROUND(AVG(scalability_index), 1) as avg_scalability,
    ROUND(AVG(reliability_score), 1) as avg_reliability,
    ROUND(AVG(innovation_index), 1) as avg_innovation,
    SUM(insights_count)::INTEGER as total_insights,
    SUM(recommendations_count)::INTEGER as total_recommendations,
    ROUND(AVG(cost_optimization_potential), 1) as avg_cost_optimization,
    ROUND(AVG(predicted_job_volume), 1) as avg_predicted_volume
  FROM business_logic_logs
  WHERE timestamp >= NOW() - (hours_back || ' hours')::INTERVAL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a comprehensive monitoring dashboard view
CREATE OR REPLACE VIEW monitoring_dashboard AS
WITH latest_system_health AS (
  SELECT 
    overall_status as system_status,
    database_latency,
    workflow_latency,
    apify_latency,
    slack_latency,
    timestamp as system_check_time
  FROM system_health_logs 
  ORDER BY timestamp DESC 
  LIMIT 1
),
latest_data_quality AS (
  SELECT 
    overall_quality as data_quality_status,
    overall_score as data_quality_score,
    data_completeness,
    schema_compliance,
    critical_issues_count,
    timestamp as quality_check_time
  FROM data_quality_logs 
  ORDER BY timestamp DESC 
  LIMIT 1
),
latest_business_logic AS (
  SELECT 
    business_health,
    business_value_score,
    efficiency_score,
    scalability_index,
    reliability_score,
    innovation_index,
    predicted_job_volume,
    cost_optimization_potential,
    timestamp as business_check_time
  FROM business_logic_logs 
  ORDER BY timestamp DESC 
  LIMIT 1
)
SELECT 
  -- System Health
  sh.system_status,
  sh.database_latency,
  sh.workflow_latency,
  sh.apify_latency,
  sh.slack_latency,
  sh.system_check_time,
  
  -- Data Quality
  dq.data_quality_status,
  dq.data_quality_score,
  dq.data_completeness,
  dq.schema_compliance,
  dq.critical_issues_count,
  dq.quality_check_time,
  
  -- Business Logic
  bl.business_health,
  bl.business_value_score,
  bl.efficiency_score,
  bl.scalability_index,
  bl.reliability_score,
  bl.innovation_index,
  bl.predicted_job_volume,
  bl.cost_optimization_potential,
  bl.business_check_time,
  
  -- Overall Status Calculation
  CASE 
    WHEN sh.system_status = 'unhealthy' OR dq.data_quality_status = 'CRITICAL' OR bl.business_health = 'CRITICAL' THEN 'CRITICAL'
    WHEN sh.system_status = 'degraded' OR dq.data_quality_status = 'POOR' OR bl.business_health = 'CONCERNING' THEN 'WARNING'
    WHEN sh.system_status = 'healthy' AND dq.data_quality_status IN ('EXCELLENT', 'GOOD') AND bl.business_health IN ('EXCELLENT', 'GOOD') THEN 'HEALTHY'
    ELSE 'DEGRADED'
  END as overall_platform_status,
  
  NOW() as dashboard_generated_at
  
FROM latest_system_health sh
CROSS JOIN latest_data_quality dq
CROSS JOIN latest_business_logic bl;
