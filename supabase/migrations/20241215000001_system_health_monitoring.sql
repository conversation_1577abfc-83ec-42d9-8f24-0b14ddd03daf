-- Create system health monitoring table
CREATE TABLE IF NOT EXISTS system_health_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  overall_status TEXT NOT NULL CHECK (overall_status IN ('healthy', 'degraded', 'unhealthy')),
  
  -- Individual service status and latency
  database_status TEXT NOT NULL CHECK (database_status IN ('healthy', 'degraded', 'error')),
  database_latency INTEGER NOT NULL DEFAULT 0,
  
  workflow_status TEXT NOT NULL CHECK (workflow_status IN ('healthy', 'degraded', 'error')),
  workflow_latency INTEGER NOT NULL DEFAULT 0,
  
  apify_status TEXT NOT NULL CHECK (apify_status IN ('healthy', 'degraded', 'error')),
  apify_latency INTEGER NOT NULL DEFAULT 0,
  
  slack_status TEXT NOT NULL CHECK (slack_status IN ('healthy', 'degraded', 'error')),
  slack_latency INTEGER NOT NULL DEFAULT 0,
  
  -- Actions taken during this health check
  actions JSONB DEFAULT '[]'::jsonb,
  
  -- Raw health check data for debugging
  raw_data JSONB,
  
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_system_health_logs_timestamp ON system_health_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_system_health_logs_overall_status ON system_health_logs(overall_status);
CREATE INDEX IF NOT EXISTS idx_system_health_logs_created_at ON system_health_logs(created_at DESC);

-- Create a composite index for time-series queries
CREATE INDEX IF NOT EXISTS idx_system_health_logs_time_status ON system_health_logs(timestamp DESC, overall_status);

-- Add RLS (Row Level Security) if needed
ALTER TABLE system_health_logs ENABLE ROW LEVEL SECURITY;

-- Create policy for service role access (for monitoring workflows)
CREATE POLICY "Service role can manage health logs" ON system_health_logs
  FOR ALL USING (auth.role() = 'service_role');

-- Create policy for authenticated users to read health logs
CREATE POLICY "Authenticated users can read health logs" ON system_health_logs
  FOR SELECT USING (auth.role() = 'authenticated');

-- Create a function to clean up old health logs (keep last 30 days)
CREATE OR REPLACE FUNCTION cleanup_old_health_logs()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM system_health_logs 
  WHERE timestamp < NOW() - INTERVAL '30 days';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a view for health status summary
CREATE OR REPLACE VIEW system_health_summary AS
SELECT 
  DATE_TRUNC('hour', timestamp) as hour,
  overall_status,
  COUNT(*) as check_count,
  AVG(database_latency) as avg_db_latency,
  AVG(workflow_latency) as avg_workflow_latency,
  AVG(apify_latency) as avg_apify_latency,
  AVG(slack_latency) as avg_slack_latency,
  MAX(timestamp) as latest_check
FROM system_health_logs 
WHERE timestamp >= NOW() - INTERVAL '24 hours'
GROUP BY DATE_TRUNC('hour', timestamp), overall_status
ORDER BY hour DESC, overall_status;
