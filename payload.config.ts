import { buildConfig } from "payload";
import { postgresAdapter } from "@payloadcms/db-postgres";
import { lexicalEditor } from "@payloadcms/richtext-lexical";

export default buildConfig({
  admin: {
    user: "users",
    // PayloadCMS admin will be available at /admin
    // Your current dashboard stays at /dashboard
  },

  // Disable GraphQL since you're using REST API only
  graphQL: {
    disable: true,
  },

  collections: [
    // Basic Users collection for PayloadCMS authentication
    {
      slug: "users",
      auth: true,
      admin: {
        useAsTitle: "email",
      },
      fields: [
        {
          name: "name",
          type: "text",
          required: true,
        },
        {
          name: "role",
          type: "select",
          options: [
            { label: "Admin", value: "admin" },
            { label: "Editor", value: "editor" },
          ],
          defaultValue: "editor",
          required: true,
        },
      ],
    },

    // Job Board Configurations - maps to your existing job_board_configs table
    {
      slug: "job-boards",
      admin: {
        useAsTitle: "name",
        description:
          "Manage job board configurations and Airtable integrations",
      },
      fields: [
        {
          name: "id",
          type: "text",
          required: true,
          unique: true,
          admin: {
            description: "Unique identifier for the job board",
          },
        },
        {
          name: "name",
          type: "text",
          required: true,
        },
        {
          name: "description",
          type: "textarea",
        },
        {
          name: "enabled",
          type: "checkbox",
          defaultValue: true,
        },
        {
          name: "airtable_base_id",
          type: "text",
          required: true,
          admin: {
            description: "Airtable Base ID (starts with app...)",
          },
        },
        {
          name: "airtable_table_name",
          type: "text",
          defaultValue: "Jobs",
        },
        {
          name: "daily_limit",
          type: "number",
          defaultValue: 10,
          min: 1,
          max: 1000,
        },
        {
          name: "posting_strategy",
          type: "select",
          options: [
            { label: "Newest First", value: "newest_first" },
            { label: "Oldest First", value: "oldest_first" },
            { label: "Random", value: "random" },
          ],
          defaultValue: "newest_first",
        },
        {
          name: "avoid_reposting_days",
          type: "number",
          defaultValue: 30,
          min: 0,
          max: 365,
        },
        {
          name: "timezone",
          type: "text",
          defaultValue: "UTC",
        },
        {
          name: "filters",
          type: "json",
          admin: {
            description: "Job filtering criteria (JSON format)",
          },
        },
      ],
    },

    // Jobs collection - read-only view of your existing jobs
    {
      slug: "jobs",
      admin: {
        useAsTitle: "title",
        description: "View and manage job listings",
        defaultColumns: ["title", "company", "status", "created_at"],
      },
      access: {
        // Start with read-only access
        create: false,
        update: false,
        delete: false,
      },
      fields: [
        {
          name: "title",
          type: "text",
          required: true,
        },
        {
          name: "company",
          type: "text",
        },
        {
          name: "description",
          type: "textarea",
        },
        {
          name: "status",
          type: "select",
          options: [
            { label: "Active", value: "active" },
            { label: "Closed", value: "closed" },
            { label: "Draft", value: "draft" },
          ],
        },
        {
          name: "source_name",
          type: "text",
          admin: {
            readOnly: true,
          },
        },
        {
          name: "processing_status",
          type: "select",
          options: [
            { label: "Pending", value: "pending" },
            { label: "Processing", value: "processing" },
            { label: "Completed", value: "completed" },
            { label: "Failed", value: "failed" },
          ],
          admin: {
            readOnly: true,
          },
        },
        {
          name: "apply_url",
          type: "text",
        },
        {
          name: "workplace_type",
          type: "text",
        },
        {
          name: "salary_min",
          type: "number",
        },
        {
          name: "salary_max",
          type: "number",
        },
        {
          name: "created_at",
          type: "date",
          admin: {
            readOnly: true,
          },
        },
      ],
    },
  ],

  editor: lexicalEditor({}),

  secret: process.env.PAYLOAD_SECRET || "your-secret-key-here",

  typescript: {
    outputFile: "./payload-types.ts",
  },

  db: postgresAdapter({
    pool: {
      connectionString: process.env.DATABASE_URL,
    },
    // Use separate schema to avoid conflicts with your existing tables
    schemaName: "payload",
    // Configure table suffixes to prevent conflicts
    localesSuffix: "_payload_locales",
    relationshipsSuffix: "_payload_rels",
    versionsSuffix: "_payload_v",
  }),
});
