# Bordfeed Test Suite

**Completely decoupled testing infrastructure** - this entire folder can be deleted without affecting application functionality.

## 🏗️ Test Structure

```
tests/
├── api/                    # API endpoint tests
│   ├── jobs.test.ts       # Jobs API (bulk processing, CRUD)
│   └── pipeline.test.ts   # Pipeline ingestion API
├── unit/                   # Pure function tests
│   ├── monitor-utils.test.ts      # Monitoring utilities
│   └── monitor-url-selection.test.ts  # URL selection logic
├── integration/            # Multi-system tests
│   └── webhooks.test.ts   # Webhook integrations
├── e2e/                    # End-to-end browser tests
│   └── monitor-status.spec.mjs  # Playwright E2E tests
├── fixtures/               # Test data
│   └── jobs.json          # Sample job data for tests
├── utils/                  # Test helpers
│   ├── openapi-validator.ts     # Schema validation utilities
├── test-results/           # Playwright test output (auto-generated)
│   ├── .last-run.json     # Test run metadata
│   └── [screenshots/videos]  # Test artifacts
├── utils/                  # Test helpers (continued)
│   └── validate-openapi.ts      # OpenAPI spec validator
└── scripts/                # Test runners
    ├── run-all-tests.ts         # Master test runner
    ├── run-unit-tests.ts        # Unit test runner
    ├── run-api-tests.ts         # API test runner
    └── run-integration-tests.ts # Integration test runner
```

## 🚀 Running Tests

### All Tests

```bash
pnpm test                    # Run complete test suite
```

### Individual Test Types

```bash
pnpm test:unit              # Unit tests only
pnpm test:api               # API tests only
pnpm test:integration       # Integration tests only
pnpm test:e2e               # End-to-end tests only
pnpm test:schema            # OpenAPI schema validation
```

### Development

```bash
pnpm test:watch             # Watch mode for unit tests
```

## 🧪 Test Types Explained

| Type            | Purpose                      | Example                            | Speed      |
| --------------- | ---------------------------- | ---------------------------------- | ---------- |
| **Unit**        | Test individual functions    | `validateAirtableBaseId()`         | ⚡ Fast    |
| **API**         | Test HTTP endpoints          | `POST /api/pipeline-ingest`        | 🚀 Medium  |
| **Integration** | Test system interactions     | Database + API + External service  | 🐌 Slower  |
| **E2E**         | Test complete user workflows | Browser automation with Playwright | 🐢 Slowest |

## 📝 Writing Tests

### Unit Tests

```typescript
// tests/unit/my-function.test.ts
import assert from "node:assert/strict";
import test from "node:test";
import { myFunction } from "../../lib/my-module";

test("should do something", () => {
  const result = myFunction("input");
  assert.equal(result, "expected");
});
```

### API Tests

```typescript
// tests/api/my-endpoint.test.ts
import assert from "node:assert/strict";
import test from "node:test";
import { testEndpoint } from "../utils/openapi-validator.js";

test("should validate API response", async () => {
  const { response, data, validation } = await testEndpoint(
    "http://localhost:3000/api/my-endpoint",
    { method: "GET" }
  );

  assert.equal(response.status, 200);
  assert.equal(validation.valid, true);
});
```

## 🔧 Test Configuration

### Environment Variables

Tests respect these environment variables:

- `NEXT_PUBLIC_BASE_URL` - API base URL (default: http://localhost:3000)
- `NODE_ENV` - Skip destructive tests in production
- `MONITOR_SECRET` - Required for monitoring tests

### OpenAPI Integration

- Tests automatically validate responses against OpenAPI schema
- Schema validation runs before all other tests
- Type-safe API testing with generated TypeScript types

## 🎯 Testing Strategy

### For Solo Development

1. **Focus on critical paths** - Test main user workflows first
2. **Schema-driven testing** - Ensure APIs match documentation
3. **Fast feedback loops** - Unit tests run in <5 seconds
4. **Confidence in deployments** - Integration tests catch breaking changes

### Test Priorities

1. **High**: Schema validation, critical API endpoints, core business logic
2. **Medium**: Integration flows, error handling, edge cases
3. **Low**: Comprehensive edge cases, performance testing

## 🛠️ Maintenance

### Adding New Tests

1. Choose the right test type (unit/api/integration/e2e)
2. Place in appropriate folder
3. Follow naming convention: `*.test.ts` or `*.spec.mjs`
4. Test runners will automatically discover new files

### Updating Tests

- Tests are completely decoupled from application code
- Update import paths if you move application files
- Regenerate OpenAPI types when API changes

### Debugging Tests

```bash
# Run specific test file
npx tsx tests/unit/my-test.test.ts

# Run with verbose output
NODE_OPTIONS="--trace-warnings" pnpm test:unit

# Debug E2E tests with visible browser
npx playwright test --headed
```

## 🔍 OpenAPI Integration

### Schema Validation

- All API responses validated against OpenAPI spec
- Catches schema drift automatically
- Ensures documentation stays in sync with implementation

### Type Generation

```bash
# Regenerate types from OpenAPI spec
pnpm run generate:types
```

## 📊 Test Results

Test runners provide detailed output:

- ✅ Passed tests with timing
- ❌ Failed tests with error details
- 📊 Summary statistics
- 🔍 Schema validation results

## 🚨 Troubleshooting

### Common Issues

1. **Import path errors**: Check relative paths from test files
2. **Environment variables**: Ensure required vars are set
3. **OpenAPI validation**: Run `pnpm test:schema` first
4. **Database tests**: Ensure test database is available

### Getting Help

- Check test output for specific error messages
- Validate OpenAPI schema if API tests fail
- Ensure development server is running for API/integration tests
