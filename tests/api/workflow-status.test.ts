/**
 * Workflow Status API Tests
 *
 * Tests the workflow status endpoint that provides dashboard data
 */

import assert from "node:assert/strict";
import test from "node:test";
import { testEndpoint } from "../utils/openapi-validator.js";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000";

interface WorkflowStatusResponse {
  stats: {
    active: number;
    completed: number;
    failed: number;
    partial_failure: number;
    total_today: number;
    jobs_processed_today: number;
  };
  queue: {
    pending: number;
    processing: number;
  };
  recent_runs: Array<{
    id: string;
    workflow_type: string;
    status: string;
    started_at: string;
    completed_at?: string;
    job_count: number;
    error_message?: string;
  }>;
  timestamp: string;
}

test("Workflow Status GET - should return status data", async () => {
  const { response, data } = await testEndpoint(
    `${BASE_URL}/api/workflows/status`,
    {
      method: "GET",
      headers: { "User-Agent": "Bordfeed-Test/1.0" },
    }
  );

  const statusData = data as WorkflowStatusResponse;

  assert.equal(response.status, 200, "Should return 200 OK");
  assert.ok(statusData.stats, "Should return stats object");
  assert.ok(statusData.queue, "Should return queue object");
  assert.ok(
    Array.isArray(statusData.recent_runs),
    "Should return recent_runs array"
  );
  assert.ok(statusData.timestamp, "Should return timestamp");

  // Validate stats structure
  assert.ok(
    typeof statusData.stats.active === "number",
    "stats.active should be number"
  );
  assert.ok(
    typeof statusData.stats.completed === "number",
    "stats.completed should be number"
  );
  assert.ok(
    typeof statusData.stats.failed === "number",
    "stats.failed should be number"
  );

  // Validate queue structure
  assert.ok(
    typeof statusData.queue.pending === "number",
    "queue.pending should be number"
  );
  assert.ok(
    typeof statusData.queue.processing === "number",
    "queue.processing should be number"
  );
});

test("Workflow Status POST - should trigger workflow", async () => {
  const { response, data } = await testEndpoint(
    `${BASE_URL}/api/workflows/status`,
    {
      method: "POST",
      headers: {
        "User-Agent": "Bordfeed-Test/1.0",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        action: "trigger_processing",
        batchSize: 5,
        source: "test",
      }),
    }
  );

  const triggerData = data as {
    success?: boolean;
    message?: string;
    workflowRunId?: string;
    messageId?: string;
  };

  assert.equal(response.status, 200, "Should return 200 OK");
  assert.equal(triggerData.success, true, "Should indicate success");
  assert.ok(triggerData.workflowRunId, "Should return workflow run ID");
  assert.ok(triggerData.message, "Should return success message");
});

test("Workflow Status POST - should reject unknown action", async () => {
  const { response, data } = await testEndpoint(
    `${BASE_URL}/api/workflows/status`,
    {
      method: "POST",
      headers: {
        "User-Agent": "Bordfeed-Test/1.0",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        action: "unknown_action",
      }),
    }
  );

  const errorData = data as { error?: string };

  assert.equal(response.status, 400, "Should return 400 Bad Request");
  assert.ok(errorData.error, "Should return error message");
});
