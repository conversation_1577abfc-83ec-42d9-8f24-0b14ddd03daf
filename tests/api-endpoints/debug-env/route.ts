import { NextResponse } from 'next/server';

export function GET() {
  // Security check - only allow in development or with secret
  const isDev = process.env.NODE_ENV === 'development';
  const hasSecret = process.env.DEBUG_SECRET === 'debug-env-check';

  if (!(isDev || hasSecret)) {
    return NextResponse.json({ error: 'Not authorized' }, { status: 403 });
  }

  return NextResponse.json({
    environment: process.env.NODE_ENV,
    workflow: {
      token: process.env.QSTASH_TOKEN ? '✅ Present' : '❌ Missing',
      baseUrl: process.env.QSTASH_URL ? '✅ Present' : '❌ Missing',
      tokenLength: process.env.QSTASH_TOKEN?.length || 0,
      // Note: Signing keys removed - direct API calls to workflows now work
    },
    other: {
      nodeEnv: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV,
    },
  });
}
