import { type NextRequest, NextResponse } from 'next/server';
import { logger } from '../../../lib/logger';

export async function POST(_request: NextRequest) {
  try {
    // Test different types of Slack notifications
    await logger.critical('🚨 TEST CRITICAL: Critical pipeline failure test', {
      source: 'test-slack-endpoint',
      test: true,
      timestamp: new Date().toISOString(),
    });

    await logger.alert('⚠️ TEST ALERT: Alert notification test', {
      source: 'test-slack-endpoint',
      test: true,
      timestamp: new Date().toISOString(),
    });

    await logger.notify('💬 TEST NOTIFY: Regular notification test', {
      source: 'test-slack-endpoint',
      test: true,
      timestamp: new Date().toISOString(),
    });

    // Test pipeline step notifications
    await logger.pipeline({
      step: 'SOURCED',
      source: 'test-apify-actor',
      jobCount: 25,
      success: true,
      metadata: { test: true },
    });

    await logger.pipeline({
      step: 'DEDUPED',
      jobCount: 25,
      success: true,
      stats: {
        processed: 25,
        duplicates: 3,
        unique: 22,
      },
      metadata: { test: true },
    });

    await logger.pipeline({
      step: 'PROCESSED',
      jobCount: 22,
      success: true,
      stats: {
        succeeded: 20,
        failed: 2,
      },
      metadata: { test: true },
    });

    await logger.pipeline({
      step: 'STORED',
      jobCount: 20,
      success: true,
      metadata: { test: true },
    });

    return NextResponse.json({
      success: true,
      message: 'Slack test notifications sent successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Slack test error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to send test notifications',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
