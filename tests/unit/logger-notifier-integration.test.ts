import assert from 'node:assert/strict';
import test, { afterEach, beforeEach, describe, mock } from 'node:test';
import { logger } from '@/lib/logger';
import type { PipelineContext } from '@/lib/notifier';

// Mock notifier
const mockNotifier = {
  critical: mock.fn(),
  alert: mock.fn(),
  info: mock.fn(),
  pipeline: mock.fn(),
};

describe('Logger-Notifier Integration', () => {
  beforeEach(() => {
    // Reset all mock calls
    mockNotifier.critical.mock.resetCalls();
    mockNotifier.alert.mock.resetCalls();
    mockNotifier.info.mock.resetCalls();
    mockNotifier.pipeline.mock.resetCalls();

    // Mock the notifier module
    mock.module('@/lib/notifier', () => ({
      createNotifier: () => mockNotifier,
    }));

    // Mock DEBUG_MODE as false
    mock.module('@/lib/constants', () => ({
      DEBUG_MODE: false,
    }));
  });

  afterEach(() => {
    mock.reset();
  });

  test('logger.critical delegates to notifier.critical with message only', () => {
    logger.critical('Critical error occurred');

    assert.equal(mockNotifier.critical.mock.callCount(), 1);
    const [message, details] = mockNotifier.critical.mock.calls[0].arguments;
    assert.equal(message, 'Critical error occurred');
    assert.equal(details, undefined);
  });

  test('logger.critical delegates to notifier.critical with message and details', () => {
    const details = { error: 'Database connection failed', code: 500 };
    logger.critical('Critical error occurred', details);

    assert.equal(mockNotifier.critical.mock.callCount(), 1);
    const [message, passedDetails] =
      mockNotifier.critical.mock.calls[0].arguments;
    assert.ok(message.includes('Critical error occurred'));
    assert.ok(message.includes('Details: {'));
    assert.ok(message.includes('Database connection failed'));
    assert.deepEqual(passedDetails, details);
  });

  test('logger.alert delegates to notifier.alert with formatted message', () => {
    const details = { warning: 'Rate limit approaching' };
    logger.alert('Warning condition detected', details);

    assert.equal(mockNotifier.alert.mock.callCount(), 1);
    const [message, passedDetails] = mockNotifier.alert.mock.calls[0].arguments;
    assert.ok(message.includes('Warning condition detected'));
    assert.ok(message.includes('Rate limit approaching'));
    assert.deepEqual(passedDetails, details);
  });

  test('logger.alert works without details', () => {
    logger.alert('Simple warning');

    assert.equal(mockNotifier.alert.mock.callCount(), 1);
    const [message, details] = mockNotifier.alert.mock.calls[0].arguments;
    assert.equal(message, 'Simple warning');
    assert.equal(details, undefined);
  });

  test('logger.notify delegates to notifier.info with formatted message', () => {
    const details = { milestone: 'System deployment complete' };
    logger.notify('Deployment successful', details);

    assert.equal(mockNotifier.info.mock.callCount(), 1);
    const [message, passedDetails] = mockNotifier.info.mock.calls[0].arguments;
    assert.ok(message.includes('Deployment successful'));
    assert.ok(message.includes('System deployment complete'));
    assert.deepEqual(passedDetails, details);
  });

  test('logger.notify works without details', () => {
    logger.notify('Simple notification');

    assert.equal(mockNotifier.info.mock.callCount(), 1);
    const [message, details] = mockNotifier.info.mock.calls[0].arguments;
    assert.equal(message, 'Simple notification');
    assert.equal(details, undefined);
  });

  test('logger.pipeline delegates to notifier.pipeline with enhanced context', () => {
    const context: PipelineContext = {
      step: 'PROCESSED',
      source: 'test-source',
      success: true,
      jobCount: 25,
      duration: 3000,
      metadata: { batchId: 'batch-456' },
    };

    logger.pipeline(context);

    assert.equal(mockNotifier.pipeline.mock.callCount(), 1);
    const [enhancedContext] = mockNotifier.pipeline.mock.calls[0].arguments;

    assert.equal(enhancedContext.step, 'PROCESSED');
    assert.equal(enhancedContext.source, 'test-source');
    assert.equal(enhancedContext.success, true);
    assert.equal(enhancedContext.jobCount, 25);
    assert.equal(enhancedContext.duration, 3000);

    assert.ok(enhancedContext.metadata?.formatted);
    assert.ok(
      enhancedContext.metadata.formatted.includes(
        '🔄 Pipeline: PROCESSED completed for test-source'
      )
    );
    assert.ok(enhancedContext.metadata.formatted.includes('25 jobs'));
    assert.ok(enhancedContext.metadata.formatted.includes('3.0s'));
    assert.ok(enhancedContext.metadata.formatted.includes('**Context:**'));
    assert.ok(
      enhancedContext.metadata.formatted.includes('"batchId": "batch-456"')
    );
  });

  test('logger.pipeline handles failed pipeline steps', () => {
    const context: PipelineContext = {
      step: 'DEDUPED',
      source: 'error-source',
      success: false,
      error: 'Deduplication service unavailable',
    };

    logger.pipeline(context);

    assert.equal(mockNotifier.pipeline.mock.callCount(), 1);
    const [enhancedContext] = mockNotifier.pipeline.mock.calls[0].arguments;

    assert.ok(
      enhancedContext.metadata?.formatted.includes(
        '❌ Pipeline: DEDUPED failed for error-source'
      )
    );
    assert.ok(
      enhancedContext.metadata.formatted.includes(
        'Error: Deduplication service unavailable'
      )
    );
  });

  test('logger.pipeline formats duration correctly', () => {
    const testCases = [
      { duration: 500, expected: '0.5s' },
      { duration: 1000, expected: '1.0s' },
      { duration: 1500, expected: '1.5s' },
      { duration: 60_000, expected: '60.0s' },
    ];

    testCases.forEach(({ duration, expected }) => {
      mockNotifier.pipeline.mock.resetCalls();

      logger.pipeline({
        step: 'TEST',
        source: 'test',
        success: true,
        duration,
      });

      const [enhancedContext] = mockNotifier.pipeline.mock.calls[0].arguments;
      assert.ok(enhancedContext.metadata?.formatted.includes(`in ${expected}`));
    });
  });

  test('logger.pipeline formats job count correctly', () => {
    const testCases = [
      { jobCount: 0, expected: '0 jobs' },
      { jobCount: 1, expected: '1 job' },
      { jobCount: 5, expected: '5 jobs' },
    ];

    testCases.forEach(({ jobCount, expected }) => {
      mockNotifier.pipeline.mock.resetCalls();

      logger.pipeline({
        step: 'TEST',
        source: 'test',
        success: true,
        jobCount,
      });

      const [enhancedContext] = mockNotifier.pipeline.mock.calls[0].arguments;
      assert.ok(enhancedContext.metadata?.formatted.includes(`(${expected})`));
    });
  });

  test('logger.pipeline handles undefined duration and jobCount', () => {
    logger.pipeline({
      step: 'TEST',
      source: 'test',
      success: true,
      // No duration or jobCount
    });

    const [enhancedContext] = mockNotifier.pipeline.mock.calls[0].arguments;
    const formatted = enhancedContext.metadata?.formatted || '';

    // Should not contain duration or job count text
    assert.ok(!formatted.includes(' in '));
    assert.ok(!formatted.includes('('));
  });
});
