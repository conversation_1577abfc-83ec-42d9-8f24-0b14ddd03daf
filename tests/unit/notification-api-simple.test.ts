import assert from 'node:assert/strict';
import test, { beforeEach, describe, mock } from 'node:test';

// Mock environment for testing
process.env.SLACK_WEBHOOK_URL = 'https://hooks.slack.com/test';

describe('Notification API Unit Tests', () => {
  beforeEach(() => {
    // Mock Supabase
    mock.module('@/lib/supabase', () => ({
      createServiceRoleClient: () => ({
        from: () => ({
          select: () => ({
            eq: () => ({
              single: () =>
                Promise.resolve({
                  data: { settings: { enableCritical: true } },
                  error: null,
                }),
            }),
          }),
          upsert: () => ({
            select: () => ({
              single: () =>
                Promise.resolve({
                  data: { settings: { enableCritical: true } },
                  error: null,
                }),
            }),
          }),
        }),
      }),
    }));

    // Mock logger
    mock.module('@/lib/logger', () => ({
      logger: {
        error: mock.fn(),
      },
    }));
  });

  test('notification settings module exports work correctly', async () => {
    const {
      getNotificationSettings,
      invalidateNotificationSettingsCache,
      DEFAULT_SETTINGS,
    } = await import('@/lib/notification-settings');

    // Test exports exist
    assert.ok(typeof getNotificationSettings === 'function');
    assert.ok(typeof invalidateNotificationSettingsCache === 'function');
    assert.ok(DEFAULT_SETTINGS);
    assert.ok(DEFAULT_SETTINGS.enableCritical === true); // Should default to true
  });

  test('notifier module exports work correctly', async () => {
    const { createNotifier, SlackNotifier, NoopNotifier } = await import(
      '@/lib/notifier'
    );

    // Test exports exist
    assert.ok(typeof createNotifier === 'function');
    assert.ok(SlackNotifier);
    assert.ok(NoopNotifier);

    // Test factory function
    const notifier = createNotifier();
    assert.ok(notifier);
  });

  test('logger module delegates correctly', async () => {
    const { logger } = await import('@/lib/logger');

    // Test logger methods exist
    assert.ok(typeof logger.critical === 'function');
    assert.ok(typeof logger.alert === 'function');
    assert.ok(typeof logger.notify === 'function');
    assert.ok(typeof logger.pipeline === 'function');

    // Test methods don't throw
    logger.critical('Test critical');
    logger.alert('Test alert');
    logger.notify('Test notify');
    logger.pipeline({
      step: 'TEST',
      source: 'test',
      success: true,
    });
  });

  test('notification settings have correct types', async () => {
    const { getNotificationSettings } = await import(
      '@/lib/notification-settings'
    );

    const settings = await getNotificationSettings();

    // Test types
    assert.ok(typeof settings.enableCritical === 'boolean');
    assert.ok(typeof settings.enableAlert === 'boolean');
    assert.ok(typeof settings.enableInfo === 'boolean');
    assert.ok(typeof settings.enablePipeline === 'boolean');
    assert.ok(typeof settings.pipelineOnlyFailures === 'boolean');
    assert.ok(settings.pipelineSteps instanceof Set);
    assert.ok(typeof settings.healthCadence === 'string');
    assert.ok(typeof settings.jobsScrapedOnComplete === 'boolean');
    assert.ok(typeof settings.jobsProcessedOnComplete === 'boolean');
    assert.ok(typeof settings.jobsProcessedDaily === 'boolean');
    assert.ok(typeof settings.jobsProcessedDailyTime === 'string');
  });
});
