import assert from 'node:assert/strict';
import test, { afterEach, beforeEach, describe, mock } from 'node:test';

// Mock data for Supabase responses
const mockSupabaseData = {
  data: null as any,
  error: null as any,
};

const mockSupabase = {
  from: mock.fn(() => ({
    select: mock.fn(() => ({
      eq: mock.fn(() => ({
        single: mock.fn(() => Promise.resolve(mockSupabaseData)),
      })),
    })),
  })),
};

// Mock console methods to avoid noise in tests
const mockConsoleError = mock.fn();

describe('Notification Settings Cache', () => {
  beforeEach(() => {
    // Reset mock data
    mockSupabaseData.data = null;
    mockSupabaseData.error = null;

    // Reset mock calls
    mockSupabase.from.mock.resetCalls();
    mockConsoleError.mock.resetCalls();

    // Mock Supabase
    mock.module('@/lib/supabase', () => ({
      createServiceRoleClient: () => mockSupabase,
    }));

    // Mock SLACK_FLAGS
    mock.module('@/lib/constants', () => ({
      SLACK_FLAGS: {
        enableCritical: true,
        enableAlert: false,
        enableInfo: false,
        enablePipeline: false,
        pipelineOnlyFailures: true,
        pipelineSteps: new Set(['SOURCED']),
      },
    }));

    // Mock console.error
    mock.module('console', () => ({
      error: mockConsoleError,
    }));
  });

  afterEach(() => {
    mock.reset();
  });

  test('returns default settings when no data in database', async () => {
    mockSupabaseData.data = null;
    mockSupabaseData.error = null;

    // Import after mocking
    const { getNotificationSettings, DEFAULT_SETTINGS } = await import(
      '@/lib/notification-settings'
    );
    const settings = await getNotificationSettings();

    assert.deepEqual(settings, DEFAULT_SETTINGS);
    assert.equal(mockSupabase.from.mock.callCount(), 1);
  });

  test('returns merged settings when data exists in database', async () => {
    const dbSettings = {
      enableCritical: false,
      enableAlert: true,
      healthCadence: '6h',
      pipelineSteps: ['SOURCED', 'PROCESSED'],
    };

    mockSupabaseData.data = { settings: dbSettings };
    mockSupabaseData.error = null;

    const { getNotificationSettings, DEFAULT_SETTINGS } = await import(
      '@/lib/notification-settings'
    );
    const settings = await getNotificationSettings();

    assert.equal(settings.enableCritical, false);
    assert.equal(settings.enableAlert, true);
    assert.equal(settings.healthCadence, '6h');
    assert.ok(settings.pipelineSteps instanceof Set);
    assert.ok(settings.pipelineSteps.has('SOURCED'));
    assert.ok(settings.pipelineSteps.has('PROCESSED'));

    // Other settings should be defaults
    assert.equal(settings.enableInfo, DEFAULT_SETTINGS.enableInfo);
  });

  test('handles pipelineSteps array conversion to Set', async () => {
    const dbSettings = {
      pipelineSteps: ['DEDUPED', 'VALIDATED', 'PROCESSED'],
    };

    mockSupabaseData.data = { settings: dbSettings };

    const { getNotificationSettings } = await import(
      '@/lib/notification-settings'
    );
    const settings = await getNotificationSettings();

    assert.ok(settings.pipelineSteps instanceof Set);
    assert.equal(settings.pipelineSteps.size, 3);
    assert.ok(settings.pipelineSteps.has('DEDUPED'));
    assert.ok(settings.pipelineSteps.has('VALIDATED'));
    assert.ok(settings.pipelineSteps.has('PROCESSED'));
  });

  test('handles empty pipelineSteps array', async () => {
    const dbSettings = {
      pipelineSteps: [],
    };

    mockSupabaseData.data = { settings: dbSettings };

    const { getNotificationSettings } = await import(
      '@/lib/notification-settings'
    );
    const settings = await getNotificationSettings();

    assert.ok(settings.pipelineSteps instanceof Set);
    assert.equal(settings.pipelineSteps.size, 0);
  });

  test('handles null pipelineSteps', async () => {
    const dbSettings = {
      pipelineSteps: null,
    };

    mockSupabaseData.data = { settings: dbSettings };

    const { getNotificationSettings, DEFAULT_SETTINGS } = await import(
      '@/lib/notification-settings'
    );
    const settings = await getNotificationSettings();

    assert.deepEqual(settings.pipelineSteps, DEFAULT_SETTINGS.pipelineSteps);
  });

  test('returns default settings when database error occurs', async () => {
    mockSupabaseData.error = {
      message: 'Connection failed',
      code: 'CONNECTION_ERROR',
    };
    mockSupabaseData.data = null;

    const { getNotificationSettings, DEFAULT_SETTINGS } = await import(
      '@/lib/notification-settings'
    );
    const settings = await getNotificationSettings();

    assert.deepEqual(settings, DEFAULT_SETTINGS);
    // Should log error (note: console mocking might not work in Node.js test runner)
  });

  test('returns default settings when PGRST116 (no rows found) error occurs', async () => {
    mockSupabaseData.error = { code: 'PGRST116', message: 'No rows found' };
    mockSupabaseData.data = null;

    const { getNotificationSettings, DEFAULT_SETTINGS } = await import(
      '@/lib/notification-settings'
    );
    const settings = await getNotificationSettings();

    assert.deepEqual(settings, DEFAULT_SETTINGS);
    // PGRST116 should not log error
  });

  test('returns default settings when fetch throws exception', async () => {
    // Make the mock throw an exception
    mockSupabase.from.mock.mockImplementation(() => {
      throw new Error('Network timeout');
    });

    const { getNotificationSettings, DEFAULT_SETTINGS } = await import(
      '@/lib/notification-settings'
    );
    const settings = await getNotificationSettings();

    assert.deepEqual(settings, DEFAULT_SETTINGS);
  });

  test('cache invalidation works correctly', async () => {
    mockSupabaseData.data = { settings: { enableCritical: false } };

    const { getNotificationSettings, invalidateNotificationSettingsCache } =
      await import('@/lib/notification-settings');

    // First call
    const settings1 = await getNotificationSettings();
    assert.equal(settings1.enableCritical, false);
    assert.equal(mockSupabase.from.mock.callCount(), 1);

    // Second call should use cache
    const settings2 = await getNotificationSettings();
    assert.equal(settings2.enableCritical, false);
    assert.equal(mockSupabase.from.mock.callCount(), 1); // Not called again

    // Invalidate cache
    invalidateNotificationSettingsCache();

    // Change mock data
    mockSupabaseData.data = { settings: { enableCritical: true } };

    // Third call should fetch fresh data
    const settings3 = await getNotificationSettings();
    assert.equal(settings3.enableCritical, true);
    assert.equal(mockSupabase.from.mock.callCount(), 2);
  });
});
