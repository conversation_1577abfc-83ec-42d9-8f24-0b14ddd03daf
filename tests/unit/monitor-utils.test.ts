import assert from 'node:assert/strict';
import test from 'node:test';
import { matchClosedPhrase } from '../../lib/monitor';

const phrases = [
  'job closed',
  'this job is no longer available',
  'applications are now closed',
] as const;

test('matchClosedPhrase detects closed phrases', () => {
  const snippet = '<p>Unfortunately this job is no longer available.</p>';
  const result = matchClosedPhrase(snippet, phrases);
  assert.equal(result.matched, true);
  assert.ok(result.phrase);
});

test('matchClosedPhrase returns false for open snippet', () => {
  const snippet = '<p>We are hiring! Apply now.</p>';
  const result = matchClosedPhrase(snippet, phrases);
  assert.equal(result.matched, false);
  assert.equal(result.phrase, null);
});
