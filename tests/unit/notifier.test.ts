import assert from 'node:assert/strict';
import test, { afterEach, beforeEach, describe, mock } from 'node:test';
import type { PipelineContext } from '@/lib/notifier';
import { createNotifier, NoopNotifier, SlackNotifier } from '@/lib/notifier';

// Test setup
let originalFetch: typeof globalThis.fetch;
let mockFetch: typeof globalThis.fetch;
let mockGetNotificationSettings: any;

// Mock notification settings
const mockDefaultSettings = {
  enableCritical: true,
  enableAlert: false,
  enableInfo: false,
  enablePipeline: false,
  pipelineOnlyFailures: true,
  pipelineSteps: new Set<string>(),
  healthCadence: 'off' as const,
  jobsScrapedOnComplete: false,
  jobsProcessedOnComplete: false,
  jobsProcessedDaily: false,
  jobsProcessedDailyTime: '09:00',
};

describe('Notifier System', () => {
  beforeEach(() => {
    // Save original fetch
    originalFetch = globalThis.fetch;

    // Create mock fetch
    mockFetch = mock.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ ok: true }),
      } as Response)
    );
    globalThis.fetch = mockFetch;

    // Mock getNotificationSettings
    mockGetNotificationSettings = mock.fn(() =>
      Promise.resolve(mockDefaultSettings)
    );

    // Dynamically mock the module
    mock.module('@/lib/notification-settings', () => ({
      getNotificationSettings: mockGetNotificationSettings,
    }));

    // Mock constants
    mock.module('@/lib/constants', () => ({
      SLACK_CONFIG: {
        webhookUrl: 'https://hooks.slack.com/test-webhook',
      },
    }));
  });

  afterEach(() => {
    // Restore original fetch
    globalThis.fetch = originalFetch;
    mock.reset();
  });

  test('createNotifier returns SlackNotifier when webhook URL is configured', () => {
    const notifier = createNotifier();
    assert.ok(notifier instanceof SlackNotifier);
  });

  test('NoopNotifier does not make network calls', async () => {
    const notifier = new NoopNotifier();

    await notifier.critical('test message');
    await notifier.alert('test message');
    await notifier.info('test message');
    await notifier.pipeline({
      step: 'SOURCED',
      source: 'test-source',
      success: true,
    });

    // Fetch should not have been called
    assert.equal(mockFetch.mock.callCount(), 0);
  });

  test('SlackNotifier sends critical notifications when enabled', async () => {
    mockGetNotificationSettings.mock.mockImplementation(() =>
      Promise.resolve({ ...mockDefaultSettings, enableCritical: true })
    );

    const notifier = new SlackNotifier('https://hooks.slack.com/test-webhook');
    await notifier.critical('Critical error occurred', { error: 'details' });

    assert.equal(mockFetch.mock.callCount(), 1);
    const [url, options] = mockFetch.mock.calls[0].arguments;
    assert.equal(url, 'https://hooks.slack.com/test-webhook');
    assert.equal(options.method, 'POST');

    const body = JSON.parse(options.body);
    assert.ok(body.text.includes('🚨 Critical error occurred'));
    assert.ok(body.text.includes('Details: {'));
  });

  test('SlackNotifier does not send critical notifications when disabled', async () => {
    mockGetNotificationSettings.mock.mockImplementation(() =>
      Promise.resolve({ ...mockDefaultSettings, enableCritical: false })
    );

    const notifier = new SlackNotifier('https://hooks.slack.com/test-webhook');
    await notifier.critical('Critical error occurred');

    assert.equal(mockFetch.mock.callCount(), 0);
  });

  test('SlackNotifier sends alert notifications when enabled', async () => {
    mockGetNotificationSettings.mock.mockImplementation(() =>
      Promise.resolve({ ...mockDefaultSettings, enableAlert: true })
    );

    const notifier = new SlackNotifier('https://hooks.slack.com/test-webhook');
    await notifier.alert('Warning message', { context: 'test' });

    assert.equal(mockFetch.mock.callCount(), 1);
    const [, options] = mockFetch.mock.calls[0].arguments;
    const body = JSON.parse(options.body);
    assert.ok(body.text.includes('⚠️ Warning message'));
  });

  test('SlackNotifier sends info notifications when enabled', async () => {
    mockGetNotificationSettings.mock.mockImplementation(() =>
      Promise.resolve({ ...mockDefaultSettings, enableInfo: true })
    );

    const notifier = new SlackNotifier('https://hooks.slack.com/test-webhook');
    await notifier.info('Info message');

    assert.equal(mockFetch.mock.callCount(), 1);
    const [, options] = mockFetch.mock.calls[0].arguments;
    const body = JSON.parse(options.body);
    assert.ok(body.text.includes('ℹ️ Info message'));
  });

  test('SlackNotifier sends pipeline notifications when enabled', async () => {
    mockGetNotificationSettings.mock.mockImplementation(() =>
      Promise.resolve({
        ...mockDefaultSettings,
        enablePipeline: true,
        pipelineOnlyFailures: false,
        pipelineSteps: new Set(),
      })
    );

    const pipelineContext: PipelineContext = {
      step: 'PROCESSED',
      source: 'test-source',
      success: true,
      jobCount: 10,
      duration: 5000,
      metadata: { batchId: 'batch-123' },
    };

    const notifier = new SlackNotifier('https://hooks.slack.com/test-webhook');
    await notifier.pipeline(pipelineContext);

    assert.equal(mockFetch.mock.callCount(), 1);
    const [, options] = mockFetch.mock.calls[0].arguments;
    const body = JSON.parse(options.body);
    assert.ok(body.text.includes('🔄 Pipeline: PROCESSED'));
    assert.ok(body.text.includes('test-source'));
    assert.ok(body.text.includes('10 jobs'));
  });

  test('SlackNotifier filters successful pipelines when pipelineOnlyFailures is true', async () => {
    mockGetNotificationSettings.mock.mockImplementation(() =>
      Promise.resolve({
        ...mockDefaultSettings,
        enablePipeline: true,
        pipelineOnlyFailures: true,
        pipelineSteps: new Set(),
      })
    );

    const notifier = new SlackNotifier('https://hooks.slack.com/test-webhook');

    // Should not send for successful pipeline
    await notifier.pipeline({
      step: 'PROCESSED',
      source: 'test-source',
      success: true,
    });
    assert.equal(mockFetch.mock.callCount(), 0);

    // Should send for failed pipeline
    await notifier.pipeline({
      step: 'PROCESSED',
      source: 'test-source',
      success: false,
    });
    assert.equal(mockFetch.mock.callCount(), 1);
  });

  test('SlackNotifier filters steps based on pipelineSteps allowlist', async () => {
    mockGetNotificationSettings.mock.mockImplementation(() =>
      Promise.resolve({
        ...mockDefaultSettings,
        enablePipeline: true,
        pipelineOnlyFailures: false,
        pipelineSteps: new Set(['SOURCED', 'DEDUPED']),
      })
    );

    const notifier = new SlackNotifier('https://hooks.slack.com/test-webhook');

    // Should not send for PROCESSED (not in allowlist)
    await notifier.pipeline({
      step: 'PROCESSED',
      source: 'test-source',
      success: true,
    });
    assert.equal(mockFetch.mock.callCount(), 0);

    // Should send for SOURCED (in allowlist)
    await notifier.pipeline({
      step: 'SOURCED',
      source: 'test-source',
      success: true,
    });
    assert.equal(mockFetch.mock.callCount(), 1);
  });

  test('SlackNotifier handles fetch errors gracefully', async () => {
    mockGetNotificationSettings.mock.mockImplementation(() =>
      Promise.resolve({ ...mockDefaultSettings, enableCritical: true })
    );

    // Mock fetch to reject
    mockFetch.mock.mockImplementation(() =>
      Promise.reject(new Error('Network error'))
    );

    const notifier = new SlackNotifier('https://hooks.slack.com/test-webhook');

    // Should not throw
    await notifier.critical('Test error');
    assert.equal(mockFetch.mock.callCount(), 1);
  });

  test('SlackNotifier handles settings fetch errors gracefully', async () => {
    mockGetNotificationSettings.mock.mockImplementation(() =>
      Promise.reject(new Error('Settings error'))
    );

    const notifier = new SlackNotifier('https://hooks.slack.com/test-webhook');

    // Should not throw and should not send notification
    await notifier.critical('Test error');
    assert.equal(mockFetch.mock.callCount(), 0);
  });
});
