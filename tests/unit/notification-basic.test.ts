import assert from 'node:assert/strict';
import test, { describe } from 'node:test';

// Simple integration test without complex mocking
describe('Notification System Basic Tests', () => {
  test('NoopNotifier can be instantiated and called', async () => {
    // Import directly to test the module loading
    const { NoopNotifier } = await import('@/lib/notifier');

    const notifier = new NoopNotifier();

    // These should not throw
    await notifier.critical('test');
    await notifier.alert('test');
    await notifier.info('test');
    await notifier.pipeline({
      step: 'TEST',
      source: 'test-source',
      success: true,
    });

    assert.ok(true, 'NoopNotifier works without errors');
  });

  test('SlackNotifier can be instantiated', async () => {
    const { SlackNotifier } = await import('@/lib/notifier');

    const notifier = new SlackNotifier('https://hooks.slack.com/test');
    assert.ok(notifier instanceof SlackNotifier);
  });

  test('createNotifier factory works', async () => {
    const { createNotifier } = await import('@/lib/notifier');

    const notifier = createNotifier();
    assert.ok(notifier, 'Factory returns a notifier');
  });

  test('logger methods exist and are callable', async () => {
    const { logger } = await import('@/lib/logger');

    assert.ok(typeof logger.critical === 'function');
    assert.ok(typeof logger.alert === 'function');
    assert.ok(typeof logger.notify === 'function');
    assert.ok(typeof logger.pipeline === 'function');

    // These should not throw (they may make network calls but shouldn't error)
    logger.critical('Test critical');
    logger.alert('Test alert');
    logger.notify('Test notify');
    logger.pipeline({
      step: 'TEST',
      source: 'test',
      success: true,
    });
  });

  test('notification settings can be loaded', async () => {
    const { getNotificationSettings, DEFAULT_SETTINGS } = await import(
      '@/lib/notification-settings'
    );

    assert.ok(DEFAULT_SETTINGS, 'Default settings exist');
    assert.ok(typeof DEFAULT_SETTINGS.enableCritical === 'boolean');
    assert.ok(DEFAULT_SETTINGS.pipelineSteps instanceof Set);

    // This may hit the database, but should work with proper env
    const settings = await getNotificationSettings();
    assert.ok(settings, 'Settings can be loaded');
    assert.ok(typeof settings.enableCritical === 'boolean');
    assert.ok(settings.pipelineSteps instanceof Set);
  });

  test('notification settings have proper structure', async () => {
    const { DEFAULT_SETTINGS } = await import('@/lib/notification-settings');

    // Verify all required fields exist with correct types
    assert.ok(typeof DEFAULT_SETTINGS.enableCritical === 'boolean');
    assert.ok(typeof DEFAULT_SETTINGS.enableAlert === 'boolean');
    assert.ok(typeof DEFAULT_SETTINGS.enableInfo === 'boolean');
    assert.ok(typeof DEFAULT_SETTINGS.enablePipeline === 'boolean');
    assert.ok(typeof DEFAULT_SETTINGS.pipelineOnlyFailures === 'boolean');
    assert.ok(DEFAULT_SETTINGS.pipelineSteps instanceof Set);
    assert.ok(
      ['off', '15m', '30m', '1h', '6h', '24h'].includes(
        DEFAULT_SETTINGS.healthCadence
      )
    );
    assert.ok(typeof DEFAULT_SETTINGS.jobsScrapedOnComplete === 'boolean');
    assert.ok(typeof DEFAULT_SETTINGS.jobsProcessedOnComplete === 'boolean');
    assert.ok(typeof DEFAULT_SETTINGS.jobsProcessedDaily === 'boolean');
    assert.ok(typeof DEFAULT_SETTINGS.jobsProcessedDailyTime === 'string');
    assert.ok(/^\d{2}:\d{2}$/.test(DEFAULT_SETTINGS.jobsProcessedDailyTime));
  });

  test('PipelineContext type validation', () => {
    // Test that we can create valid pipeline contexts
    const validContexts = [
      {
        step: 'SOURCED',
        source: 'test-source',
        success: true,
      },
      {
        step: 'PROCESSED',
        source: 'another-source',
        success: false,
        error: 'Something went wrong',
        jobCount: 25,
        duration: 5000,
        metadata: { batchId: 'batch-123' },
      },
    ];

    validContexts.forEach((context) => {
      assert.ok(context.step);
      assert.ok(context.source);
      assert.ok(typeof context.success === 'boolean');
    });
  });

  test('system integration - logger delegates to notifier', async () => {
    const { logger } = await import('@/lib/logger');

    // This is an integration test - it tests the actual wiring
    // It may make network calls but should not throw errors
    let errorThrown = false;

    try {
      logger.critical('Integration test critical');
      logger.alert('Integration test alert');
      logger.notify('Integration test notify');
      logger.pipeline({
        step: 'INTEGRATION_TEST',
        source: 'test-suite',
        success: true,
        jobCount: 1,
        duration: 100,
      });
    } catch (_error) {
      errorThrown = true;
    }

    assert.ok(!errorThrown, 'Logger delegation should not throw errors');
  });
});
