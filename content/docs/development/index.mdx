---
title: Development Guide
description: Comprehensive guide for developers working with Bordfeed
---

This section contains technical documentation for developers working with Bordfeed, including environment setup, debugging, testing, and deployment procedures.

## Quick Start

```bash
# Clone and install
git clone <your-repo-url>
cd bordfeed
pnpm install

# Environment setup
pnpm run env:pull # Pull all secrets from Vercel to .env.local

# Start development
pnpm dev  # Starts on http://localhost:3000
```

## Development Topics

### Environment & Setup

- **[Environment Variables](./environment-setup)** - Complete setup guide for all required environment variables
- **[Local Development](./local-setup)** - Setting up your development environment
- **[Deployment Guide](./deployment)** - Production deployment procedures

### Debugging & Testing

- **[Webhook Debugging](./webhook-debugging)** - Comprehensive webhook debugging and logging guide
- **[Testing Guide](./testing)** - Testing strategies and best practices
- **[Mock Server](./mock-server)** - Setting up mock servers for development

### Integration Development

- **[Webhook Integration](./webhook-integration)** - Building and testing webhook integrations
- **[API Development](./api-development)** - Developing new API endpoints
- **[Database Operations](./database)** - Working with Supabase and database operations

## Development Philosophy

### Core Principles

**Leverage Existing Infrastructure** - Don't reinvent the wheel. Use proven services like Apify, Upstash, Vercel, Supabase, Slack, GitHub, OpenAI, Vercel AI SDK, shadcn/ui, Next.js, TypeScript, and Ultracite.

**Simplicity & Maintainability** - Keep the project simple and easy to understand:

- Single file, single function, single responsibility
- Code should be DRY, easy to understand, and easy to maintain
- Fast development iteration is crucial

**Focus Over Features** - Simplify ruthlessly. We use a **two-phase processing approach**:

**Phase 1 (Webhooks):** Fast minimal extraction + complete raw storage, programmatic and free  
**Phase 2 (AI):** Centralized extraction of all structured fields from raw data, AI-powered and paid

### Pipeline Stages

1. **Job Sourcing** (Apify) →
2. **Minimal Extraction & Storage** (Webhooks → Supabase) →
3. **AI Processing** (QStash Scheduler) →
4. **Publishing** (Airtable) →
5. **Monitoring** (Multi-layer)

**Cloud-First Architecture** - Leverage existing cloud services so a single founder/developer can focus on core value creation rather than infrastructure management.

**Async Processing** - We never need real-time processing for job post data. If operations take time, that's acceptable - optimize for reliability over speed.

**End-to-End Functionality** - Every feature must work completely end-to-end, including customization capabilities and multi-platform publishing.

## Development Guidelines

- **Production Ready** - All code must be production-quality from day one
- **Type Safety** - Full TypeScript with strict mode throughout
- **Code Quality** - 100% Ultracite compliance with no linting errors
- **Accessibility** - ARIA compliance and keyboard navigation support
- **Performance** - Optimized patterns and best practices

## Tech Stack

### Core Technologies

- **Next.js 15** - React framework with App Router and Turbopack
- **React 19** - Latest React with concurrent features
- **TypeScript** - Strict type safety throughout
- **Tailwind CSS** - Utility-first styling
- **shadcn/ui** - Modern component library

### Backend Services

- **Supabase** - PostgreSQL database with real-time capabilities
- **OpenAI GPT-4o-mini** - AI-powered job data extraction
- **Vercel AI SDK** - Latest AI SDK with enhanced Zod v4 support
- **Upstash QStash** - Queue management and job scheduling
- **Apify** - Reliable web scraping and data extraction

### State & Validation

- **Zustand** - Lightweight state management
- **Zod v4** - Advanced schema validation and type inference

### Development Tools

- **Ultracite** - Code quality and accessibility linting
- **Playwright** - End-to-end testing
- **Vitest** - Unit testing framework
- **pnpm** - Fast, efficient package manager

## Getting Help

For development questions and support:

1. **Check Documentation**: Start with the relevant guide in this section
2. **Review Code Examples**: Look at existing implementations
3. **Test Locally**: Use the development environment for testing
4. **Debug Systematically**: Use the debugging guides and tools
5. **Ask for Help**: Create an issue or reach out for assistance

## Contributing

When contributing to Bordfeed:

1. **Follow Standards**: Adhere to the development philosophy and guidelines
2. **Write Tests**: Include appropriate test coverage
3. **Document Changes**: Update documentation for new features
4. **Quality Checks**: Ensure code passes all quality checks
5. **Production Ready**: Code should be deployment-ready
