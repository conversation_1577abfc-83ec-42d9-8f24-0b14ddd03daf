---
title: Testing Infrastructure
description: Comprehensive guide to Bordfeed's decoupled testing system with OpenAPI integration
---

Bordfeed uses a **completely decoupled testing infrastructure** that can be deleted without affecting application functionality. Our testing strategy balances thorough coverage with rapid development cycles, optimized for solo development.

## Architecture Overview

### Decoupled Design

The entire `tests/` folder is **completely separate** from application code:

```bash
# This won't break your app
rm -rf tests/
```

All tests import from the application but never the reverse, ensuring clean separation and maintainability.

### Test Organization

```
tests/
├── api/           # HTTP endpoint tests
├── unit/          # Pure function tests  
├── integration/   # Multi-system tests
├── e2e/           # Browser automation tests
├── fixtures/      # Test data
├── utils/         # Test helpers
└── scripts/       # Test runners
```

## Test Types Explained

| Type | Purpose | Speed | When to Use |
|------|---------|-------|-------------|
| **Unit** | Test individual functions | ⚡ Fast (< 5s) | Pure logic, utilities, validation |
| **API** | Test HTTP endpoints | 🚀 Medium | Request/response validation |
| **Integration** | Test system interactions | 🐌 Slower | Database + API + External services |
| **E2E** | Test complete workflows | 🐢 Slowest | Critical user journeys |

## Running Tests

### Quick Commands

```bash
# Run everything
pnpm test

# Individual test types
pnpm test:unit         # Fast unit tests
pnpm test:api          # API endpoint tests
pnpm test:integration  # System integration tests
pnpm test:e2e          # Playwright browser tests
pnpm test:schema       # OpenAPI validation

# Development
pnpm test:watch        # Watch mode for rapid feedback
```

### Test Output

Our test runners provide detailed feedback:

```bash
🚀 Running Unit Tests

🧪 Running monitor-utils.test.ts...
✅ monitor-utils.test.ts passed (1719ms)

📊 Unit Test Summary:
====================
✅ monitor-url-selection.test.ts: 1865ms
✅ monitor-utils.test.ts: 1719ms

2/2 unit tests passed
Total time: 3584ms
```

## OpenAPI Integration

### Schema-Driven Testing

Every API test automatically validates responses against our OpenAPI specification:

```typescript
import { testEndpoint } from '../utils/openapi-validator.js';

test('Pipeline API validates against schema', async () => {
  const { response, data, validation } = await testEndpoint(
    'http://localhost:3000/api/pipeline-ingest',
    { method: 'GET' }
  );
  
  // Automatic schema validation
  assert.equal(validation.valid, true);
  assert.equal(response.status, 200);
});
```

### Benefits

- **Prevents documentation drift** - Tests fail if API changes don't match docs
- **Type safety** - Generated TypeScript types from OpenAPI spec
- **Contract testing** - Ensures API works exactly as documented
- **Faster development** - Catch breaking changes immediately

## Writing Tests

### Unit Tests

Test pure functions and business logic:

```typescript
// tests/unit/validation.test.ts
import assert from 'node:assert/strict';
import test from 'node:test';
import { validateAirtableBaseId } from '../../lib/validation';

test('should validate Airtable base ID format', () => {
  assert.equal(validateAirtableBaseId('appABC123DEF456789'), true);
  assert.equal(validateAirtableBaseId('invalid'), false);
});
```

### API Tests

Test HTTP endpoints with schema validation:

```typescript
// tests/api/health.test.ts
import assert from 'node:assert/strict';
import test from 'node:test';
import { testEndpoint } from '../utils/openapi-validator.js';

test('Health endpoint returns valid response', async () => {
  const { response, data, validation } = await testEndpoint(
    'http://localhost:3000/api/health'
  );
  
  assert.equal(response.status, 200);
  assert.equal(validation.valid, true);
  assert.ok(data.status);
});
```

### Integration Tests

Test multiple systems working together:

```typescript
// tests/integration/pipeline.test.ts
test('Complete job processing workflow', async () => {
  // 1. Submit job via API
  // 2. Verify database storage
  // 3. Check external service integration
  // 4. Validate final state
});
```

### E2E Tests

Test complete user workflows with Playwright:

```typescript
// tests/e2e/job-monitoring.spec.mjs
import { test, expect } from '@playwright/test';

test('User can monitor job status', async ({ page }) => {
  await page.goto('/dashboard/jobs');
  await page.click('[data-testid="monitor-job"]');
  await expect(page.locator('.monitoring-result')).toBeVisible();
});
```

## Test Data Management

### Fixtures

Reusable test data in `tests/fixtures/`:

```json
// tests/fixtures/jobs.json
{
  "validJob": {
    "content": "Senior Software Engineer...",
    "sourceUrl": "https://example.com/job/123",
    "metadata": { "company": "TechCorp" }
  }
}
```

### Environment Handling

Tests automatically adapt to environment:

```typescript
// Skip destructive tests in production
if (process.env.NODE_ENV === 'production') {
  test.skip('Skipping in production environment');
  return;
}
```

## Testing Strategy

### Solo Developer Optimized

Our approach prioritizes:

1. **Fast feedback loops** - Unit tests run in seconds
2. **Critical path coverage** - Focus on main user workflows  
3. **Schema validation** - Ensure APIs work as documented
4. **Maintainable tests** - Clear organization and reusable patterns

### Test Priorities

**High Priority (implement first):**
- Schema validation tests
- Critical API endpoints
- Core business logic
- Error handling

**Medium Priority:**
- Integration flows
- Edge cases
- Performance validation

**Low Priority:**
- Comprehensive edge cases
- Load testing (defer until scaling)

## Continuous Integration

### Pre-deployment Validation

```bash
# Full test suite before deployment
pnpm test:schema    # Validate OpenAPI spec
pnpm test:unit      # Fast unit tests
pnpm test:api       # API contract tests
pnpm test:e2e       # Critical workflows
```

### Development Workflow

```bash
# During development
pnpm test:watch     # Continuous unit testing
pnpm test:api       # After API changes
pnpm test:schema    # After OpenAPI updates
```

## Troubleshooting

### Common Issues

**Import path errors:**
```bash
# Check relative paths from test files
import { myFunction } from '../../lib/my-module';
```

**Environment variables:**
```bash
# Ensure required vars are set
export NEXT_PUBLIC_BASE_URL=http://localhost:3000
```

**OpenAPI validation failures:**
```bash
# Validate schema first
pnpm test:schema
```

### Debugging Tests

```bash
# Run specific test file
npx tsx tests/unit/my-test.test.ts

# Verbose output
NODE_OPTIONS="--trace-warnings" pnpm test:unit

# Debug E2E with visible browser
npx playwright test --headed
```

## Best Practices

### Test Organization

- **One test file per module** - Mirror your application structure
- **Descriptive test names** - Clearly state what's being tested
- **Group related tests** - Use consistent naming patterns

### Performance

- **Fast unit tests** - Keep under 5 seconds total
- **Efficient API tests** - Use real endpoints but minimize database hits
- **Smart E2E testing** - Focus on critical user journeys only

### Maintenance

- **Regular schema validation** - Run `pnpm test:schema` after API changes
- **Update fixtures** - Keep test data current with real application data
- **Clean test isolation** - Each test should be independent

## Advanced Features

### Custom Test Utilities

Our `tests/utils/` folder provides:

- **OpenAPI validator** - Automatic schema validation
- **Test fixtures** - Reusable test data
- **API helpers** - Simplified endpoint testing
- **Database utilities** - Test data setup/cleanup

### Performance Monitoring

Test runners track:

- **Execution time** - Identify slow tests
- **Success rates** - Monitor test reliability  
- **Schema compliance** - Track API contract adherence

This testing infrastructure ensures **high confidence deployments** while maintaining **rapid development velocity** perfect for solo development that can scale as your team grows.
