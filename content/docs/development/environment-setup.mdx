---
title: Environment Variables Setup
description: Complete guide to setting up environment variables for Bordfeed development
---

## Quick Start

```bash
pnpm run env:pull # Pull all secrets from Vercel to .env.local

pnpm dev # Start development at http://localhost:3000
```

## Environment Strategy

Bordfeed uses a **Vercel-first environment workflow**:

- Environment variables are stored in Vercel dashboard
- Local development pulls secrets using `pnpm run env:pull`
- No `.env` files in version control

## Required Secrets (Store in Vercel)

### Crypto Keys (Generate Once)

```bash
# Encryption key for Airtable PAT storage (32 bytes)
node -e "console.log('SECRETS_ENCRYPTION_KEY=' + require('crypto').randomBytes(32).toString('hex'))"

# API endpoint protection keys (16 bytes each)
node -e "console.log('MONITOR_SECRET=' + require('crypto').randomBytes(16).toString('hex'))"
node -e "console.log('SCHEDULER_SECRET=' + require('crypto').randomBytes(16).toString('hex'))"
node -e "console.log('ADMIN_SECRET_KEY=' + require('crypto').randomBytes(16).toString('hex'))"
```

### Core Services

```bash
OPENAI_API_KEY=sk-proj-your-openai-key
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
NEXT_PUBLIC_BASE_URL=https://bordfeed.com
```

### Apify Integration

```bash
APIFY_TOKEN=your-apify-api-token
APIFY_BASE_URL=https://api.apify.com/v2
JOBDATAAPI_ACTOR_ID=your-jobdataapi-actor-id
WORKABLE_ACTOR_ID=your-workable-actor-id
WWR_ACTOR_ID=your-wwr-actor-id
```

### Upstash (QStash)

```bash
QSTASH_TOKEN=your-qstash-token
QSTASH_CURRENT_SIGNING_KEY=your-qstash-signing-key
QSTASH_NEXT_SIGNING_KEY=your-qstash-next-signing-key
QSTASH_URL=https://qstash.upstash.io
```

### Monitoring & Notifications

```bash
SLACK_WEBHOOK_URL=your-slack-webhook
NEXT_PUBLIC_MONITOR_SECRET=your-monitor-public-key
```

## Optional Integrations

```bash
JOBDATA_API_KEY=YOUR_API_KEY
# Note: Airtable credentials are now configured per job board in the database
# AIRTABLE_PAT, AIRTABLE_BASE_ID, AIRTABLE_TABLE_NAME are no longer used
```

## Deduplication Configuration

All deduplication settings have sensible defaults:

```bash
DEDUP_ENABLED=true
DEDUP_DEBUG=false
DEDUP_LEVEL1_ENABLED=true
DEDUP_LEVEL2_ENABLED=true
DEDUP_LEVEL3_ENABLED=true
DEDUP_LEVEL1_TTL_DAYS=30
DEDUP_LEVEL2_TTL_DAYS=30
DEDUP_LEVEL3_TTL_DAYS=7
```

## Auto-Generated (Vercel provides these)

```bash
VERCEL_OIDC_TOKEN=auto-generated-by-vercel
```

## Setup Steps

### 1. Generate Crypto Keys

Run the crypto key generation commands above (only needed once per deployment).

### 2. Add Variables to Vercel

1. Go to [Vercel Dashboard](https://vercel.com/dashboard)
2. Navigate to Project Settings → Environment Variables
3. Add all required variables
4. **Set for all environments** (Production, Preview, Development)

### 3. Pull Locally

```bash
pnpm run env:pull
```

### 4. Test Setup

```bash
pnpm dev
```

### 5. Deploy

```bash
vercel --prod
```

## Environment Variable Categories

### Public Variables (`NEXT_PUBLIC_*`)

These are exposed to the browser:

- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- `NEXT_PUBLIC_BASE_URL`
- `NEXT_PUBLIC_MONITOR_SECRET`

### Server-Only Variables

These are only available on the server:

- `OPENAI_API_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`
- `APIFY_TOKEN`
- `QSTASH_TOKEN`
- `SECRETS_ENCRYPTION_KEY`

### Security Keys

These protect internal endpoints:

- `MONITOR_SECRET` - Protects monitoring endpoints
- `SCHEDULER_SECRET` - Protects scheduler endpoints
- `ADMIN_SECRET_KEY` - Protects admin operations
- `SECRETS_ENCRYPTION_KEY` - Encrypts stored credentials

## Troubleshooting

### Common Issues

#### "Environment variable not found"

1. Check variable name spelling
2. Verify variable is set in Vercel dashboard
3. Run `pnpm run env:pull` to refresh local environment
4. Restart development server

#### "Invalid environment variable"

1. Check variable format and encoding
2. Verify no extra spaces or characters
3. For crypto keys, regenerate if corrupted

#### "Permission denied on env:pull"

1. Ensure you're authenticated with Vercel CLI: `vercel login`
2. Check you have access to the project
3. Verify project is linked: `vercel link`

### Debugging Environment

```bash
# Check which variables are loaded
node -e "console.log(Object.keys(process.env).filter(k => k.includes('SUPABASE')))"

# Test specific variable
node -e "console.log('OPENAI_API_KEY length:', process.env.OPENAI_API_KEY?.length || 'NOT_SET')"
```

## Security Best Practices

### Variable Management

1. **Never commit secrets** to version control
2. **Use Vercel dashboard** for all secret storage
3. **Rotate keys regularly** for production systems
4. **Use different keys** for different environments

### Access Control

1. **Limit Vercel project access** to necessary team members
2. **Use service accounts** where possible
3. **Monitor variable access** through Vercel logs
4. **Audit permissions regularly**

### Development Safety

1. **Use `.env.local`** for local development only
2. **Don't share .env.local files** between developers
3. **Pull fresh secrets** when switching branches
4. **Clear local environment** when debugging

## Notes

- Secrets are stored in Vercel by default, not in `.env` files
- Use `pnpm run env:pull` to sync Vercel secrets to `.env.local`
- Crypto keys only need to be generated once per deployment
- Deduplication settings have sensible defaults and rarely need adjustment
- `VERCEL_OIDC_TOKEN` is automatically generated by Vercel CLI
- Optional variables can be added later as needed
