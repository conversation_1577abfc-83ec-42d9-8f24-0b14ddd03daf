{"name": "bordfeed", "version": "0.1.7", "description": "AI-powered job board automation platform with intelligent extraction, monitoring, and multi-platform publishing", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "npx ultracite lint", "format": "npx ultracite format", "test": "npx tsx tests/scripts/run-all-tests.ts", "test:unit": "npx tsx tests/scripts/run-unit-tests.ts", "test:api": "npx tsx tests/scripts/run-api-tests.ts", "test:integration": "npx tsx tests/scripts/run-integration-tests.ts", "test:e2e": "npx playwright test", "test:schema": "npx tsx tests/utils/validate-openapi.ts", "test:watch": "npx tsx --watch tests/scripts/run-unit-tests.ts", "env:pull": "vercel env pull", "env:push": "vercel env push", "deploy": "vercel --prod", "postinstall": "fumadocs-mdx"}, "dependencies": {"@ai-sdk/openai": "2.0.8", "@fastify/deepmerge": "^3.1.0", "@payloadcms/db-postgres": "^3.50.0", "@payloadcms/next": "^3.50.0", "@payloadcms/richtext-lexical": "^3.50.0", "@payloadcms/ui": "^3.50.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@scalar/api-client-react": "^1.3.27", "@scalar/api-reference-react": "^0.7.38", "@scalar/nextjs-api-reference": "^0.8.14", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.54.0", "@tanstack/react-table": "^8.21.3", "@types/mdx": "^2.0.13", "@types/pg": "^8.15.5", "@upstash/workflow": "^0.2.17", "ai": "5.0.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "fumadocs-core": "^15.6.9", "fumadocs-mdx": "^11.7.4", "fumadocs-openapi": "^9.1.10", "fumadocs-ui": "^15.6.9", "graphql": "^16.11.0", "lucide-react": "^0.539.0", "next": "15.4.6", "next-themes": "0.4.6", "nuqs": "^2.4.3", "openai": "^5.12.2", "openapi-fetch": "^0.14.0", "payload": "^3.50.0", "pg": "^8.16.3", "react": "^19.1.1", "react-dom": "^19.1.1", "recharts": "^3.1.2", "sharp": "^0.34.3", "shiki": "^3.9.2", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "use-debounce": "^10.0.5", "vercel": "^44.7.3", "zod": "^4.0.17", "zustand": "^5.0.7"}, "devDependencies": {"@biomejs/biome": "2.1.4", "@faker-js/faker": "^9.9.0", "@playwright/test": "^1.54.2", "@redux-devtools/extension": "^3.3.0", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^22.17.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "openapi-typescript": "^7.8.0", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5.9.2", "ultracite": "5.1.2", "vitest": "^3.2.4"}}